
<br /><div class="card" style="height: 1000px;"> <!-- style="flex: 1 1 auto;overflow: hidden;max-height: calc(105vh - 120px);min-height: calc(95vh - 120px);-->
    <div class="card-header fuseqCardHeader">
        DASHBOARD
       <!-- <p-dropdown [options]="favoriteList" [(ngModel)]="selectedDash" optionLabel="Code" optionValue="Id" (onChange)="selectedDashFunction()"></p-dropdown>-->
        <select *ngIf="loadData == true" [(ngModel)]="selectedDash" (ngModelChange)="selectedDashFunction()">
          <option *ngFor="let c of favoriteList" [value]="c.Id">{{c.Code}}</option>
        </select>
        <button *ngIf="showDateFilter == true"  style="float: right;" class="btn btn-grey ml-3" aria-hidden="true"  (click)="showFilteModal(); showColFilters3 = !showColFilters3"><i *ngIf="showColFilters3" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters3" class=" pi pi-filter-slash"></i>
          Date filter
        </button>
      
    </div>
    <!--  <tabset>
     
  <tab *ngFor="let tabz of favoriteList"
    [heading]="tabz.Code"
    (selectTab)="onRowSelect(tabz.Id)"></tab></tabset>-->
    <div class="card-body fuseqCardBody"> <!--style="height: calc(102vh - 206px)"-->
    <div [hidden]="dashboard.length != 0" class="row justify-content-center" style="font-size: large;font-weight: bold;margin-top: 25px;text-transform: uppercase;"> select dashboard as landing page</div> 
      <div  style="display: block;height: 100%;">
 <!--    <gridster *ngIf="loadData == true"  [options]="options"
        [ngStyle]="dashboardType == 'View' ?  {'background':'#ffffff'} : ''">
            <gridster-item [item]="item" [ngStyle]="dashboardType == 'View' ?  {'border': '1px solid #2a5d89','border-radius': '0.25rem'} : ''" *ngFor="let item of dashboard; let i = index">
         --> 
         
         
    <gridster *ngIf="loadData == true"  [options]="options"  [ngStyle]="(dashboardType == 'View') ?  {'background':'#fafafa'} : ''">
      <gridster-item [item]="item" 
        [ngStyle]="(dashboardType == 'View') ?  {'border': 'initial',
        'border-radius': '0 0 1rem 1rem',
        'box-shadow': '0 5px 11px #0000002e, 0 4px 15px #00000026', 'overflow' : 'auto'} : ''" *ngFor="let item of dashboard; let i = index">  
          <div class="button-holder">
            <div  class="row justify-content-center" *ngIf="item.showname" style="margin-right: 0px !important; margin-left: 0px !important;font-size: 14px;font-weight: bold;"><b></b> &nbsp;{{item.widgetDetails.widgetName}}</div>
            <div  class="row justify-content-center"  style="margin-right: 0px !important; margin-left: 0px !important; font-size: 12px; font-weight: bold;">
              <div *ngIf="item.showdescription"><b></b>&nbsp;{{item.widgetDetails.widgetDescription}}</div>
              <div *ngIf="item.collapsed"  style="margin-left:0.4rem !important; margin-top: -0.1rem;">
                <button  style="font-size:10px;border: unset;" class="btn btn-outline-info btn-sm" data-toggle="modal" tooltip="Info" container="body"(click)="clickDescription(item.widgetDetails.widgetDescription)" >
                  <i class="fa fa-info-circle" aria-hidden="true"></i>
                </button>
            <div class="invalid-feedback" >{{monthVar}}</div>
              </div>
           </div>

                    <div class="gridster-item-content">
                        <div class="item-buttons">
                        <button [hidden]="hideDetailsOnView == true" class="btn btn-sm btn-grey float-right" (click)="removeItem(i)">
                            <i class="fa fa-trash"></i>
                        </button>
                        <div  class="btn-group float-right" dropdown container="gridster" [hidden]="hideDetailsOnView == true" >
                            <button id="button-basic" dropdownToggle type="button" class="btn btn-gray btn-sm dropdown-toggle"
                            aria-controls="dropdown-basic">
                            <i class="fa fa-cog"></i> <span class="caret"></span>
                            </button>
                            <ul id="dropdown-basic" *dropdownMenu class="dropdown-menu"
                            role="menu" aria-labelledby="button-basic">
                            <li role="menuitem" *ngIf="!item.checkshared" ><a class="dropdown-item" (click)="cloneWidget(item)">Clone</a></li>
                            <li role="menuitem"><a class="dropdown-item" (click)="showWidgetModal(i,'Edit')">Edit</a></li>
                            <!--<li class="divider dropdown-divider"></li>
                            <li role="menuitem">
                            <a class="dropdown-item" (click)="showWidgetModal()">Visualizza</a>
                            </li>-->
                            </ul>
                            </div>
                        </div>
                        <i [ngClass]="item.icon"></i>

                    </div>
                </div>

                <div class="col sm-4" id="count" style="display: contents;" [hidden]="isSaved == false || item.widgetDetails.Type != 'count'">
                  <div *ngIf="!item.conferror" class="chartjs-container">
                  <div class="clickable"  style="display: flex;" data-toggle="modal" >
  
                      <!--"dialogClass==='pdialogCreate' ? 'pdialogcreate' : dialogClass==='pdialogEdit' ? 'pdialogedit' : 'pdialogview'-->
                   <!--<div class="callout callout-info "
                    [ngStyle]="dialogClass==='pdialogCreate' ? {'width': '150px', 'border-left-color':'#2b897a'} :
                    dialogClass==='pdialogEdit' ? {'width': '150px', 'border-left-color':'#fe9800'} :
                    {'width': '150px', 'border-left-color':'#2196f3'}">
                      <small class="text-muted"style="white-space:nowrap">Count - {{item.option}}</small>
                              <br>
                              <strong class="h4">{{item.data}}</strong>
                    </div>-->
                    <div class="brand-card1" style="padding: 0px !important;">
                      <div class="count-box1">
                        <div class="count-title11" style="white-space: nowrap;"><u>{{item.option}}</u></div>
                        <div class="count-title22">{{item.data}}</div>
                      </div></div>
                   </div>
                  </div>
                  <div *ngIf="item.conferror"> 
                    <div class="box-header with-border" style="padding-top: 20px;text-align: center;">
          <!--<p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Key licence expired</b></p>-->
          <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Unable to convert the value of the 'result' column to an integer. Ensure all values are numeric</b></p>
          </div>
                </div>
                </div>


                <div *ngIf="item.type == 'contract' && item.contractWidgetData" class="clickable sm-4" style="display: contents;" [hidden]="item.widgetDetails.Type != 'contract'">
                  <div *ngIf= "item.contractWidgetData.contractvalid == true" class="box-body" style="display: grid;">
                    <div class="box box-default box-solid" style="border: 1px solid #999; border-radius:0.3rem 0.3rem 1rem 1rem ">
                      <div class="card-header fuseqCardHeader" style="text-align: center; padding:3px">
                      <h3 class="box-title" style="font-size: 14px; color: #f2f2f2;font-weight: bold; ">{{item.contractWidgetData.name_contract}}
                  <!--<button [hidden] = "item.contractWidgetData.count_version_contract <= 1"  style="font-size:10px;border: unset;" class="btn btn-outline-info btn-sm" data-toggle="modal" tooltip="Info" container="body" (click)="clickDetails('version', item.contractWidgetData.contract_id,'version')" >
                        <i class="pi pi-plus-circle" style="font-size:10px;border: unset; color: #ffffff" aria-hidden="true"></i>
                      </button>-->  </h3>
                      </div>
             
  
                      <div class="box-header with-border" style="padding-top: 10px;text-align: center;">
                        <p class="description-text" style="font-size: 0.8rem;font-weight: bold;"><b>{{ item.contractWidgetData.labelMonth}} {{ item.contractWidgetData.labelYear}}</b></p>
                        </div>
                        <hr>
              <!--       <div class="box-header with-border" style="text-align: center;">
                      <p class="description-text" style="font-size: 15px">Indicators: {{item.contractWidgetData.TotKPIs}}</p>
                      </div>-->
  
                      <div class="box-body2" >
                        <table class="table table-striped" style="margin-bottom: 0rem;text-align: center;">
                          <tr>
                            <td style="vertical-align: middle; font-size: 0.8rem; border-top: unset;">
                              <p class="description-text" style="font-size: 15px;font-weight: bold;">Metrics: {{item.contractWidgetData.totcalkpis}}/{{item.contractWidgetData.TotKPIs}}
                                <!-- / <button (click)="TotContractKPIsClick(item.contractWidgetData)" style="border: none; background: none; font-size: inherit; font-weight: bold; color: inherit; cursor: pointer;">
                              {{item.contractWidgetData.TotContractKPIs}}
                             </button>  -->
                              </p>
                            </td>  
                           <td style="vertical-align: middle; font-size: 0.8rem; border-left: 1px solid #c8ced3; border-top: unset; ">
                              <p-button [disabled]= "item.contractWidgetData.indicatorOthers == '0'" label="Inactive" styleClass="p-button-outlined p-button-secondary" badgeClass="p-badge-secondary " badge={{item.contractWidgetData.indicatorOthers||0}} (onClick)="clickDetails('inactive', item.contractWidgetData.contract_id,'inactive')"></p-button>
                          </td>
                           <!-- <td style="vertical-align: middle; font-size: 0.8rem; border-left: 1px solid #c8ced3; border-top: unset;">
                            <p-button label="Inactive" styleClass="p-button-outlined p-button-secondary" badgeClass="p-badge-secondary" badge="0"></p-button>
                        </td>-->

                      
                          </tr>
                        <tr>
                            <td style="vertical-align: middle; font-size: 0.8rem; border-top: unset;">
                              <p-button  [disabled]= "item.contractWidgetData.day == '0'" label="Day" styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.day||0}} (onClick)="clickDetails('active', item.contractWidgetData.contract_id,'day')"></p-button>
                            </td>   <td style="vertical-align: middle; font-size: 0.8rem; border-left: 1px solid #c8ced3; border-top: unset; ">
                              <p-button  [disabled]= "item.contractWidgetData.week == '0'" label="Week"  styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.week||0}} (onClick)="clickDetails('active', item.contractWidgetData.contract_id,'week')"></p-button>
                      
                          </td>
                      
                          </tr>
                          <tr>
                            <td style="vertical-align: middle; font-size: 0.8rem; border-top: unset;">
                              <p-button  [disabled]= "item.contractWidgetData.month == '0'" label="Month"  styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.month||0}} (onClick)="clickDetails('active', item.contractWidgetData.contract_id,'month')"></p-button>
                      
                           </td>   <td style="vertical-align: middle; font-size: 0.8rem; border-top: unset; border-left: 1px solid #c8ced3;">
                            <p-button [disabled]= "item.contractWidgetData.quarter == '0'" label="Quarter" styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.quarter||0}} (onClick)="clickDetails('active', item.contractWidgetData.contract_id,'quarter')"></p-button>
                            
                          <!--    <p-button [disabled]= "item.contractWidgetData.half_year == '0'" label="Half" styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.half_year||0}} (onClick)="clickDetails('active', item.contractWidgetData.contract_id,'half-year')"></p-button>
                         --> </td>
                          </tr>
                          <tr ><td style="vertical-align: middle; font-size: 0.8rem; border-top: unset;">
                           
                              <p-button [disabled]= "item.contractWidgetData.half_year == '0'" label="Half-year" styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.half_year||0}} (onClick)="clickDetails('active', item.contractWidgetData.contract_id,'half-year')"></p-button>
                          </td>
                             <td style="vertical-align: middle; font-size: 0.8rem; border-top: unset; border-left: 1px solid #c8ced3;">
                              <p-button  [disabled]= "item.contractWidgetData.year == '0'" label="Year"  styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.year||0}} (onClick)="clickDetails('active', item.contractWidgetData.contract_id,'year')"></p-button>
                            </td>   
                          </tr>
                        </table>
                        </div>
                      <div class="box-body2">
                        <table class="table table-striped tableStatusWidget" style="margin-bottom: 0rem;">
                          <tr style="border-left: 3px solid #009688; border-right: 3px solid #009688">
                            <td style="vertical-align: middle; font-size: 0.8rem"> <a>Compliant</a>
                                <button [disabled] ="item.contractWidgetData.compliant == 0"  class="p-element p-ripple p-button-rounded p-button-success p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #009688; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('compliant', item.contractWidgetData.contract_id, 'compliant')">
                                <span class="p-button-label">{{item.contractWidgetData.compliant}}</span>
                                </button>
                           </td>
                          </tr>
                          <tr style="border-left: 3px solid #FF3D57;border-right: 3px solid #FF3D57">
                            <td style="vertical-align: middle; font-size: 0.8rem"> <a>Not compliant</a>
                              <button [disabled] ="item.contractWidgetData.notcompliant == 0" class="p-element p-ripple p-button-rounded p-button-danger p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #FF3D57; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('noncompliant', item.contractWidgetData.contract_id, 'notcompliant')">
                                <span class="p-button-label">{{item.contractWidgetData.notcompliant}}</span>
                              </button> </td>
                          </tr>
                          <tr style="border-left: 3px solid #569bbd;border-right: 3px solid #569bbd">
                            <td style="vertical-align: middle; font-size: 0.8rem"> <a>Others</a>
                                <button [disabled] ="item.contractWidgetData.indicatorOthersNull == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="background-color: #3faacd;  height: 1.4rem; width: 1.4rem; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('othersnull', item.contractWidgetData.contract_id, 'othersnull')">
                                <span class="p-button-label">{{item.contractWidgetData.indicatorOthersNull}}</span>
                              </button></td>
                          </tr>
                          <tr style="border-left: 3px solid #db890d;border-right: 3px solid #db890d">
                            <td style="vertical-align: middle; font-size: 0.8rem"> <a>Pending</a>
                                <button [disabled] ="item.contractWidgetData.pending == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style=" background-color: #db890d; height: 1.4rem; width: 1.4rem; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('pending', item.contractWidgetData.contract_id, 'pending')">
                                <span class="p-button-label">{{item.contractWidgetData.pending}}</span>
                              </button></td>
                          </tr>
                          <tr style="border-left: 3px solid #ffd700;border-right: 3px solid #ffd700">
                            <td style="vertical-align: middle; font-size: 0.8rem"> <a>Not calculated</a>
                                <button [disabled] ="item.contractWidgetData.notcalculated == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style=" background-color: #ffd700;height:1.4rem; width: 1.4rem; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('notcalculated', item.contractWidgetData.contract_id, 'notcalculated')">
                                <span class="p-button-label">{{item.contractWidgetData.notcalculated}}</span>
                              </button></td>
                          </tr>
                         <tr style="border-left: 3px solid #607D8B;border-right: 3px solid #607D8B">
                            <td style="vertical-align: middle; font-size: 0.8rem"> <a>Inactive</a>
                                <button [disabled] ="item.contractWidgetData.indicatorOthers == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('inactive', item.contractWidgetData.contract_id, 'inactive')">
                                <span class="p-button-label">{{item.contractWidgetData.indicatorOthers}}</span>
                              </button></td>
                          </tr>
                         
                        </table>
                      </div>
                  <!--   <div >
                        <table class="table table-striped" style="margin-bottom: 0rem;">
                          <tr *ngIf="item.contractWidgetData.dayTracking != 0" style="border-left: 3px solid #2196f3;">
                            <td style="vertical-align: middle; font-size: 0.8rem; text-align: center;"> <a>Calculation status </a>
                             </td>
                          </tr>
                         <tr  *ngIf="item.contractWidgetData.dayTracking != 0" style="border-left: 3px solid #2196f3;">
                          <td style="vertical-align: middle; font-size: 0.8rem">
                            <p-button  [disabled]= "item.contractWidgetData.day == 0" label="Day" styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.day||0}} (click)="clickDetails('active', item.contractWidgetData.contract_id,'day')"></p-button>
                          </td>
                            <td style="vertical-align: middle; font-size: 0.8rem">
                              <button [disabled] ="item.contractWidgetData.dayTracking == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; float: right;  margin-left: 4px;  font-size: 0.7rem; opacity : unset; background-color: #0065b8;" data-toggle="modal" (click)="clickCalculationStatus('calculationday', item.contractWidgetData.contract_id, 'calculationday')">
                                <span class="p-button-label">{{item.contractWidgetData.dayTracking}}</span>
                              </button>
                              <button [disabled] ="item.contractWidgetData.dayNotcalculated == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;" data-toggle="modal" (click)="clickCalculationStatus('dayNotcalculated', item.contractWidgetData.contract_id, 'dayNotcalculated')">
                                <span class="p-button-label">{{item.contractWidgetData.dayNotcalculated}}</span>
                              </button>
                              <button  [disabled] ="item.contractWidgetData.dayNotcompliant == 0" class="p-element p-ripple p-button-rounded p-button-danger p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; background-color: #FF3D57; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;"  data-toggle="modal" (click)="clickCalculationStatus('dayNotcompliant', item.contractWidgetData.contract_id, 'dayNotcompliant')">
                                <span class="p-button-label">{{item.contractWidgetData.dayNotcompliant}}</span>
                              </button>
                             <button  [disabled] ="item.contractWidgetData.dayCompliant == 0" class="p-element p-ripple p-button-rounded p-button-success p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; background-color: #009688; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;" data-toggle="modal" (click)="clickCalculationStatus('dayCompliant', item.contractWidgetData.contract_id, 'dayCompliant')">
                                <span class="p-button-label">{{item.contractWidgetData.dayCompliant}}</span>
                              </button>
                            </td>
                          </tr>
                        </table>
                      </div>  -->
                      <div class="box-body2">
                        <table class="table table" style="margin-bottom: 0rem;">
                          <tr style="text-align: center;">
                            <td  colspan="2" style="vertical-align: middle; font-size: 0.8rem; text-align: center;"> <a><b>Calculation status</b> </a>
                             </td>
                          </tr>
                         <tr >
                          <td style="vertical-align: middle; font-size: 0.8rem">
                            <p-button  [disabled]= "item.contractWidgetData.day == '0'" label="Day" styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.day||0}} (onClick)="clickDetails('active', item.contractWidgetData.contract_id,'day')"></p-button>
                          </td>
                            <td style="vertical-align: middle; font-size: 0.8rem">
                              <button [disabled] ="item.contractWidgetData.dayTracking == 0" container="body" tooltip="Total Periods" class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; float: right;  margin-left: 4px;  font-size: 0.7rem; opacity : unset; background-color: #0065b8;" data-toggle="modal" (click)="clickCalculationStatus('calculationday', item.contractWidgetData.contract_id, 'calculationday')">
                                <span class="p-button-label">{{item.contractWidgetData.dayTracking}}</span>
                              </button>
                              <button [disabled] ="item.contractWidgetData.dayNotcalculated == 0"  tooltip="Not calculated" container="body" class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #ffd700; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;"data-toggle="modal" (click)="clickCalculationStatus('dayNotcalculated', item.contractWidgetData.contract_id, 'dayNotcalculated')">
                                <span class="p-button-label">{{item.contractWidgetData.dayNotcalculated}}</span>
                              </button>
                                 <button  [disabled] ="item.contractWidgetData.dayPending	 == 0"  tooltip="Pending" container="body" class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #e99a25; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;"  data-toggle="modal" (click)="clickCalculationStatus('dayPending', item.contractWidgetData.contract_id, 'dayPending')">
                                  <span class="p-button-label">{{item.contractWidgetData.dayPending	}}</span> <!---->
                                </button>
                              <button  [disabled] ="item.contractWidgetData.dayIndicatorOthersNull == 0" tooltip="Others" container="body" class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #3faacd; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;"  data-toggle="modal" (click)="clickCalculationStatus('dayIndicatorOthersNull', item.contractWidgetData.contract_id, 'dayIndicatorOthersNull')">
                                <span class="p-button-label">{{item.contractWidgetData.dayIndicatorOthersNull}}</span> <!---->
                              </button>
                              <button  [disabled] ="item.contractWidgetData.dayNotcompliant == 0"  container="body" tooltip="Not compliant" class="p-element p-ripple p-button-rounded p-button-danger p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #FF3D57; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;"  data-toggle="modal" (click)="clickCalculationStatus('dayNotcompliant', item.contractWidgetData.contract_id, 'dayNotcompliant')">
                                <span class="p-button-label">{{item.contractWidgetData.dayNotcompliant}}</span>
                              </button>                         
                             <button  [disabled] ="item.contractWidgetData.dayCompliant == 0"
                              class="p-element p-ripple p-button-rounded p-button-success p-button p-component p-button-icon-only" tooltip="Compliant"  container="body"
                               style="height: 1.4rem; width: 1.4rem; background-color: #009688; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;" data-toggle="modal" (click)="clickCalculationStatus('dayCompliant', item.contractWidgetData.contract_id, 'dayCompliant')">
                                <span class="p-button-label">{{item.contractWidgetData.dayCompliant}}</span>
                              </button>
                            </td>
                          </tr>
                          <tr >
                            <td style="vertical-align: middle; font-size: 0.8rem">
                              <p-button  [disabled]= "item.contractWidgetData.week == '0'" label="Week" styleClass="p-button-outlined" badgeClass="p-badge-info" badge={{item.contractWidgetData.week||0}} (onClick)="clickDetails('active', item.contractWidgetData.contract_id,'week')"></p-button>
                            </td>
                              <td style="vertical-align: middle; font-size: 0.8rem">
                                <button [disabled] ="item.contractWidgetData.weekTracking == 0"  tooltip="Total Periods"  container="body"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; float: right;  margin-left: 4px;  font-size: 0.7rem; opacity : unset; background-color: #0065b8;" data-toggle="modal" (click)="clickCalculationStatus('calculationweek', item.contractWidgetData.contract_id, 'calculationweek')">
                                  <span class="p-button-label">{{item.contractWidgetData.weekTracking}}</span>
                                </button>
                                <button [disabled] ="item.contractWidgetData.weekNotcalculated == 0"  tooltip="Not calculated"
                                 container="body" class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #ffd700; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;" data-toggle="modal" (click)="clickCalculationStatus('weekNotcalculated', item.contractWidgetData.contract_id, 'weekNotcalculated')">
                                  <span class="p-button-label">{{item.contractWidgetData.weekNotcalculated}}</span>
                                </button>
                                 <button  [disabled] ="item.contractWidgetData.weekPending == 0" tooltip="Pending"  container="body" class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #e99a25; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;"  data-toggle="modal" (click)="clickCalculationStatus('weekPending', item.contractWidgetData.contract_id, 'weekPending')">
                                    <span class="p-button-label">{{item.contractWidgetData.weekPending}}</span> <!---->
                                  </button>
                                <button  [disabled] ="item.contractWidgetData.weekIndicatorOthersNull == 0" tooltip="Others"  container="body" class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #3faacd; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;"  data-toggle="modal" (click)="clickCalculationStatus('weekIndicatorOthersNull', item.contractWidgetData.contract_id, 'weekIndicatorOthersNull')">
                                  <span class="p-button-label">{{item.contractWidgetData.weekIndicatorOthersNull}}</span> <!---->
                                </button>
                                <button  [disabled] ="item.contractWidgetData.weekNotcompliant == 0" tooltip="Not compliant"  container="body" class="p-element p-ripple p-button-rounded p-button-danger p-button p-component p-button-icon-only" style="height: 1.4rem; width: 1.4rem; background-color: #FF3D57; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;"  data-toggle="modal" (click)="clickCalculationStatus('weekNotcompliant', item.contractWidgetData.contract_id, 'weekNotcompliant')">
                                  <span class="p-button-label">{{item.contractWidgetData.weekNotcompliant}}</span>
                                </button>
                                <button  [disabled] ="item.contractWidgetData.weekCompliant == 0"
                                class="p-element p-ripple p-button-rounded p-button-success p-button p-component p-button-icon-only" tooltip="Compliant" container="body"
                                 style="height: 1.4rem; width: 1.4rem; background-color: #009688; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;" data-toggle="modal" (click)="clickCalculationStatus('weekCompliant', item.contractWidgetData.contract_id, 'weekCompliant')">
                                  <span class="p-button-label">{{item.contractWidgetData.weekCompliant}}</span>
                                </button>
                              </td>
                            </tr>
                        </table>
                      </div>
                    </div>
            </div>
          <!--  <div *ngIf = "item.contractWidgetData.contractvalid == true" class="box-body">
              <div class="box box-default box-solid" style="border: 1px solid #999">
                <div class="box-header with-border" style="padding:3px;text-align: center;background-color: #2196f3;">
                <h3 class="box-title" style="font-size: 12px; color: #f2f2f2; ">{{item.contractWidgetData.name_contract}}
                <button [hidden] = "item.contractWidgetData.count_version_contract == 1"  style="font-size:10px;border: unset;" class="btn btn-outline-info btn-sm" data-toggle="modal" tooltip="Info" container="body" (click)="clickDetails('version', item.contractWidgetData.contract_id,'version')" >
                  <i class="pi pi-plus-circle" style="font-size:10px;border: unset; color: #ffffff" aria-hidden="true"></i>
                </button> </h3>
                </div>
                <div class="box-header with-border" style="padding:1px;text-align: center; border-left: 3px solid #2196f3">
                  <p class="description-text" style="font-size: 0.8rem">{{ item.contractWidgetData.labelMonth}} {{ item.contractWidgetData.labelYear}}</p>
                  </div>
               <div class="box-header with-border" style="padding:1px;text-align: center; border-left: 3px solid #2196f3;">
                <p class="description-text" style="font-size: 0.8rem">Indicators: {{item.contractWidgetData.TotKPIs}}</p>
                </div>
              <div class="box-body" style="margin-top: -10px;">
                  <div  style="display : flex;  flex-wrap: wrap; text-align: center !important">
                    <div class="col-sm-6"  style="border-left: 3px solid #2196f3; ">
                     <button  [disabled] ="item.contractWidgetData.day == 0" class="p-element p-ripple p-button-rounded p-button p-component p-button-icon-only"  style="height: 1rem; width: 1.1rem;background-color: #2196f3; font-size: 0.7rem; opacity : unset"data-toggle="modal" (click)="clickDetails('active', item.contractWidgetData.contract_id,'day')">
                      {{item.contractWidgetData.day}}
                     </button>
                     <p class="description-text " style="font-size: 0.8rem;" >Daily</p>
                   </div>
                      <div class="col-sm-6" style="border-left: 1px solid #c8ced3; " >
                    <button [disabled] ="item.contractWidgetData.month == 0" class="p-element p-ripple p-button-rounded p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem;background-color: #2196f3; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('active',item.contractWidgetData.contract_id, 'month')">
                          {{item.contractWidgetData.month}}
                         </button>
                        <p class="description-text" style="font-size: 0.8rem;">Monthly</p>
                      </div>
                      <div class="col-sm-6" style="border-left: 3px solid #2196f3;padding: 10px" >
                       <button [disabled] ="item.contractWidgetData.quarter == 0" class="p-element p-ripple p-button-rounded p-button p-component p-button-icon-only"  style="height: 1rem; width: 1.1rem;background-color: #2196f3; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('active',item.contractWidgetData.contract_id,'quarter')">
                          {{item.contractWidgetData.quarter}}
                         </button>
                        <p class="description-text"  style="font-size: 0.8rem">Quarterly</p>
                      </div>
                      <div class="col-sm-6" style="border-left: 1px solid #c8ced3;padding: 10px ">
                      <button [disabled] ="item.contractWidgetData.half_year == 0" class="p-element p-ripple p-button-rounded p-button p-component p-button-icon-only"  style="height: 1rem; width: 1.1rem;background-color: #2196f3; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('active',item.contractWidgetData.contract_id,'half-year')">
                          {{item.contractWidgetData.half_year}}
                        </button>
                        <p class="description-text"  style="font-size: 0.8rem">Half-yearly</p>
                      </div>
                      <div class="col-sm-6" style="border-left: 3px solid #2196f3; padding: 10px">
                        <button [disabled] ="item.contractWidgetData.year == 0" class="p-element p-ripple p-button-rounded p-button p-component p-button-icon-only" style="height:1rem; width: 1.1rem;background-color: #2196f3; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('active',item.contractWidgetData.contract_id,'year')">
                          {{item.contractWidgetData.year}}
                        </button>
                        <p class="description-text"  style="font-size: 0.8rem">Annual</p>
                      </div>
                      <div class="col-sm-6" style="border-left: 1px solid #c8ced3;padding: 10px ">
                        <button  [disabled] ="item.contractWidgetData.indicatorOthers == 0" class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('inactive', item.contractWidgetData.contract_id,'inactive')">
                          <span class="p-button-label">{{item.contractWidgetData.indicatorOthers}}</span>
                        </button>
                        <p class="description-text"  style="font-size: 0.8rem">Others</p>
  
  
                      </div>
                    </div>
                </div>
                <div class="box-body2">
                  <table class="table table-striped" style="margin-bottom: 0rem;">
                    <tr style="border-left: 3px solid #009688">
                      <td style="vertical-align: middle; font-size: 0.8rem"> <a>Compliant</a>
                          <button [disabled] ="item.contractWidgetData.compliant == 0"  class="p-element p-ripple p-button-rounded p-button-success p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; background-color: #009688; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('compliant', item.contractWidgetData.contract_id, 'compliant')">
                          <span class="p-button-label">{{item.contractWidgetData.compliant}}</span>
                          </button>
                     </td>
                    </tr>
                    <tr style="border-left: 3px solid #FF3D57;">
                      <td style="vertical-align: middle; font-size: 0.8rem"> <a>Not compliant</a>
                        <button [disabled] ="item.contractWidgetData.notcompliant == 0" class="p-element p-ripple p-button-rounded p-button-danger p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; background-color: #FF3D57; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('noncompliant', item.contractWidgetData.contract_id, 'notcompliant')">
                          <span class="p-button-label">{{item.contractWidgetData.notcompliant}}</span>
                        </button> </td>
                    </tr>
                    <tr style="border-left: 3px solid #607D8B;">
                      <td style="vertical-align: middle; font-size: 0.8rem"> <a>Not calculated</a>
                          <button [disabled] ="item.contractWidgetData.notcalculated == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('notcalculated', item.contractWidgetData.contract_id, 'notcalculated')">
                          <span class="p-button-label">{{item.contractWidgetData.notcalculated}}</span>
                        </button></td>
                    </tr>
                   <tr style="border-left: 3px solid #607D8B;">
                      <td style="vertical-align: middle; font-size: 0.8rem"> <a>Others</a>
                          <button [disabled] ="item.contractWidgetData.indicatorOthers == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; float: right; font-size: 0.7rem; opacity : unset" data-toggle="modal" (click)="clickDetails('inactive', item.contractWidgetData.contract_id, 'inactive')">
                          <span class="p-button-label">{{item.contractWidgetData.indicatorOthers}}</span>
                        </button></td>
                    </tr>
                    <tr *ngIf="item.contractWidgetData.dayTracking != 0" style="border-left: 3px solid #2196f3;">
                      <td style="vertical-align: middle; font-size: 0.8rem; text-align: center;"> <a>Calculation status </a>
                       </td>
                    </tr>
                   <tr  *ngIf="item.contractWidgetData.dayTracking != 0" style="border-left: 3px solid #2196f3;">
                      <td style="vertical-align: middle; font-size: 0.8rem"> <a>Daily</a>
  
  
                        <button [disabled] ="item.contractWidgetData.dayTracking == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; float: right;  margin-left: 4px;  font-size: 0.7rem; opacity : unset; background-color: #0065b8;" data-toggle="modal" (click)="clickCalculationStatus('calculationday', item.contractWidgetData.contract_id, 'calculationday')">
                          <span class="p-button-label">{{item.contractWidgetData.dayTracking}}</span>
                        </button>
                        <button [disabled] ="item.contractWidgetData.dayNotcalculated == 0"  class="p-element p-ripple p-button-rounded p-button-secondary p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;" data-toggle="modal" (click)="clickCalculationStatus('dayNotcalculated', item.contractWidgetData.contract_id, 'dayNotcalculated')">
                          <span class="p-button-label">{{item.contractWidgetData.dayNotcalculated}}</span>
                        </button>
                        <button  [disabled] ="item.contractWidgetData.dayNotcompliant == 0" class="p-element p-ripple p-button-rounded p-button-danger p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; background-color: #FF3D57; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;"  data-toggle="modal" (click)="clickCalculationStatus('dayNotcompliant', item.contractWidgetData.contract_id, 'dayNotcompliant')">
                          <span class="p-button-label">{{item.contractWidgetData.dayNotcompliant}}</span>
                        </button>
                       <button  [disabled] ="item.contractWidgetData.dayCompliant == 0" class="p-element p-ripple p-button-rounded p-button-success p-button p-component p-button-icon-only" style="height: 1rem; width: 1.1rem; background-color: #009688; float: right; font-size: 0.8rem; opacity : unset; margin-left: 4px;" data-toggle="modal" (click)="clickCalculationStatus('dayCompliant', item.contractWidgetData.contract_id, 'dayCompliant')">
                          <span class="p-button-label">{{item.contractWidgetData.dayCompliant}}</span>
                        </button>
  
                        <button  [disabled] ="item.contractWidgetData.day == 0" class="p-element p-ripple p-button-rounded p-button p-component p-button-icon-only"  style="height: 1rem; width: 1.1rem;background-color: #2196f3; float: right;  font-size: 0.7rem; opacity : unset"data-toggle="modal" (click)="clickDetails('active', item.contractWidgetData.contract_id,'day')">
                          <span class="p-button-label">{{item.contractWidgetData.day}}</span>
                        </button>
                      </td>
                    </tr>
                  </table>
                </div>
              </div>
      </div>                   -->
            <div  *ngIf= "item.contractWidgetData.contractvalid == false" class="box-body">
              <div class="box box-default box-solid" style="border: 1px solid #999; border-radius:0.3rem 0.3rem 1rem 1rem ">
                <div class="card-header fuseqCardHeader" style="text-align: center; padding:3px">
                <h3 class="box-title" style="font-size: 12px; color: #f2f2f2; ">{{item.contractWidgetData.name_contract}}</h3>
              </div>
              </div> 
  
              <div class="box-header with-border" style="padding:1px;text-align: center;">
                <p class="description-text" style="font-size: 0.8rem"><b>{{ item.contractWidgetData.labelMonth}} {{ item.contractWidgetData.labelYear}}</b></p>
                <p class="description-text" style="font-size: 0.8rem;">Contract or Contract Party not valid</p>
                </div>
            </div>
            </div>


<!--   SURVEY-->
<div *ngIf="item.widgetDetails.Type == 'survey' &&  item.surveyData" class="clickable sm-4"   style="display: contents;" [hidden]="item.widgetDetails.Type != 'survey'">

  <div *ngIf="item.noData == true" class="box-header with-border" style="padding-top: 20px;text-align: center;">
    <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>No data found for the given year</b></p>
    </div>
  <div class="box-body" style="display: contents;">
    <div class="scroll-container">
    <div class="box box-default box-solid"> <!--" style="border: 1px solid #999; border-radius: 0px 0px 1rem 1rem;"-->
      <div class="card-header fuseqCardHeader" style="text-align: center; padding:8px">
      <h3 class="box-title" style="font-size: 13px; color: #f2f2f2;;margin:0 "> CONTRACT EXPERIENCE DASHBOARD - YEAR: {{item.surveyData.year}} </h3>
      </div>    
      <div   class="box box-default box-solid" style="min-height: 35%; max-height: 40%; margin-right: 3rem; margin-left: 3rem; margin-top: 25px;border: 1px solid #999; border-radius: 1.1rem 1.1rem 1rem 1rem">
<div class="card-header fuseqCardHeader" style="text-align: center; padding:6px; border-radius: calc(1rem - 1px) calc(1rem - 1px) 0 0 !important;">
      <h3 class="box-title" style="font-size: 12px; color: #f2f2f2;margin:0 ">CONTRACTS</h3>
      </div>
  <div  class="brand-card-body" style=" margin-bottom: 1rem !important;">
    <div class="container"  style="min-width: 100%;">
 <!--     <h2  class="group-title" style="font-size: 14px;"><b>Contract</b></h2> -->              
     <div class="count-container">      
     <div   class="count-box"  (click)="showModalDetails('contract_completed', item.surveyData.year)">
      <div class="count-title"><u>COMPLETED</u></div>  
      <div class="count-title2" style="margin-top: 10px">{{item.surveyData.contractComplete}} / {{ item.surveyData.contractAll}}</div>
   
       <div class="count-title2" style="margin-top: 3px"> {{item.surveyData.contractComplete_percentage}} %</div>                
     </div>
     <div    class="count-box"  (click)="showModalDetails('contract_onGoing', item.surveyData.year)">
      <div class="count-title"><u>ON GOING-SCHEDULED</u></div>  
       <div class="count-title2"  style="margin-top: 10px">{{item.surveyData.contractStart}} /  {{ item.surveyData.contractAll}}</div>
    
       <div class="count-title2" style="margin-top: 3px">{{item.surveyData.contractStart_percentage}} %</div>                
     </div>
     <div   class="count-box"   (click)="showModalDetails('contract_threshold', item.surveyData.year)">
      <div class="count-title"><u>BELOW PERCENTAGE</u></div>
     
  <!--    <div class="count-title">Contract % /<span style="color: red;">{{ total }}</span></div>   -->
  <div class="count-title2"  style="margin-top: 10px"><span style="color: red;">{{item.surveyData.below_threshold_count}} </span> / {{ item.surveyData.contractAll}}</div>
 
     <div class="count-title2" style="margin-top: 3px"> {{item.surveyData.below_threshold_percentage}} % </div>    
    
    </div>
    <div   class="count-box"   (click)="showModalDetails('contract_average', item.surveyData.year)">
      <div class="count-title"><u>BELOW AVERAGE</u></div>
     
  <!--    <div class="count-title">Contract % /<span style="color: red;">{{ total }}</span></div>   -->
  <div class="count-title2"  style="margin-top: 10px"><span style="color: red;">{{item.surveyData.below_average_count}} </span> / {{ item.surveyData.below_average_total}}</div>
 
     <div class="count-title2" style="margin-top: 3px"> {{item.surveyData.below_average_percentage}} % </div>    
    
    </div>
  <!--   <div   class="count-box"   (click)="showModalDetails('expired_survey')">
       <div class="count-title">Process: </div>
       <div class="count-title">2024: </div>          
     </div>
     <div  class="count-box"  (click)="showModalDetails('expired_survey')" >
       <div class="count-title">Contract: </div>
       <div class="count-title">2024: </div>
      </div>-->

     </div>
    </div>
  </div>  
    </div> 
   <div   class="box box-default box-solid" style="min-height: 35%; max-height: 40%; margin-right: 3rem; margin-left: 3rem; margin-top: 25px;border: 1px solid #999; border-radius: 1.1rem 1.1rem 1rem 1rem">
      <div class="card-header fuseqCardHeader" style="text-align: center; padding:6px; border-radius: calc(1rem - 1px) calc(1rem - 1px) 0 0 !important;">
       <!--  0.3 0.3 1 1 
         <div   class="brand-card" style="min-height: 25%; max-height: 60%; margin-right: 5px; margin-left: 15px;margin-top: -4px;">  
        
        -->
              <h3 class="box-title" style="font-size: 12px; color: #f2f2f2;margin:0 ">CAMPAIGNS</h3>
              </div>
          <div  class="brand-card-body" style=" margin-bottom: 1rem !important;">
            <div class="container" style="min-width: 100%;">             
             <div class="count-container">      
             <div   class="count-box"  (click)="showModalDetails('compaign_completed',  item.surveyData.year)">
              <div class="count-title"><u>COMPLETED</u></div>
              <div class="count-title2" style="margin-top: 10px">{{ item.surveyData.campaignCompleted}} / {{item.surveyData.campaignAll}}</div>          
               <div class="count-title2" style="margin-top: 3px"> {{item.surveyData.campaignComplete_percentage}} %</div>                
             </div>
             <div    class="count-box"  (click)="showModalDetails('compaign_start', item.surveyData.year)">
              <div class="count-title"><u>ON GOING-SCHEDULED</u></div>
               <div class="count-title2" style="margin-top: 10px">{{item.surveyData.campaignStart}} /  {{ item.surveyData.campaignTotalSchedule}}</div>
               <div class="count-title2" style="margin-top: 3px">{{item.surveyData.campaignStart_percentage}} %</div>                
             </div>
             <div   class="count-box"   (click)="showModalDetails('compaign_threshold', item.surveyData.year) ">
          <div class="count-title"><u>BELOW PERCENTAGE</u></div>
          <div class="count-title2" style="margin-top: 10px"><span style="color: red;">{{item.surveyData.campaign_threshold_count}} </span> / {{ item.surveyData.campaign_threshold_total}}</div>
             <div class="count-title2" style="margin-top: 3px"> {{item.surveyData.campaign_threshold_percentage}} % </div>                 
            </div>     
            <div   class="count-box"   (click)="showModalDetails('compaign_average', item.surveyData.year) ">
              <div class="count-title"><u>BELOW AVERAGE</u></div>
              <div class="count-title2" style="margin-top: 10px"><span style="color: red;"> {{item.surveyData.below_campaign_average_count}} </span> / {{ item.surveyData.below_campaign_average_total}}</div>
                 <div class="count-title2" style="margin-top: 3px"> {{item.surveyData.below_campaign_average_percentage}} % </div>                 
                </div>    
             </div>
            </div>     
          </div>         
            </div>
            <div   class="box box-default box-solid" style="min-height: 35%; max-height: 40%; margin-right: 3rem; margin-left: 3rem; margin-top: 25px;border: 1px solid #999; border-radius: 1.1rem 1.1rem 1rem 1rem">
              <div class="card-header fuseqCardHeader" style="text-align: center; padding:6px; border-radius: calc(1rem - 1px) calc(1rem - 1px) 0 0 !important;">   
                <h3 class="box-title" style="font-size: 12px; color: #f2f2f2;margin:0 ">USERS</h3>
                </div>
            <div  class="brand-card-body" style=" margin-bottom: 1rem !important;">
              <div class="container"  style="min-width: 100%;">             
                <div class="count-container">      
                <div   class="count-box"  (click)="showModalDetails('user_tosend', item.surveyData.year)">
                 <div class="count-title"><u>TO SEND</u></div>
                 <div class="count-title2" style="margin-top: 10px"><span style="color: red;">{{ item.surveyData.userOpen}} </span> / {{ item.surveyData.userAll }}</div>   
              <!--   <div class="count-title" style="margin-top: 10px"><u>TOTAL CAMPAIGNS</u></div>  
                 <div class="count-title" style="margin-top: 10px"><span style="color: red;">{{ item.surveyData.userTosend}} </span> / {{ item.surveyData.userTosendTotal }}</div>  
               -->      <div class="count-title" style="height: 20px"></div>    
               </div>                                   
               <div   class="count-box"   (click)="showModalDetails('user_details', item.surveyData.year)">
                <div class="count-title"><u>PERCENTAGE TO SEND</u></div>         
                   <div class="count-title2" style="margin-top: 10px"> {{ item.surveyData.userPercentage}} % </div>     
              <!---->           <div class="count-title" style="height: 20px"> </div>          
                  </div>  
                  <div   class="count-box"   (click)="showModalDetails('user_details', item.surveyData.year)">
                    <div class="count-title"><u>AVERAGE USERS IN CAMPAIGNS</u></div>         
                       <div class="count-title2" style="margin-top: 10px"> {{ item.surveyData.userAverage}} </div>       
                     <!---->       <div class="count-title" style="height: 20px"></div>      
                      </div>    
                </div>
               </div>  
       
           </div>
         </div>
         <div   class="box box-default box-solid" style="min-height: 35%; max-height: 40%; margin-right: 3rem; margin-left: 3rem; margin-top: 25px;border: 1px solid #999; border-radius: 1.1rem 1.1rem 1rem 1rem">
          <div class="card-header fuseqCardHeader" style="text-align: center; padding:6px; border-radius: calc(1rem - 1px) calc(1rem - 1px) 0 0 !important;">   
            <h3 class="box-title" style="font-size: 13px; color: #f2f2f2;margin:0 "><b>MEASURES</b></h3>
            </div>
           
    
             <div  class="brand-card-body" style=" margin-bottom: 1rem !important; ">
            
              <div class="measure-scroll-container">  
              <div class="count-container"  style="min-width: 1390px;margin-bottom: 1rem;">      
             <!--  <div   class="count-box"  (click)="showModalDetails('MeasureMaxMin')" >--> 
              <div class="count-box2">
                <table style="width: 100%;">
                  <tr>
                    <td style="vertical-align: top; padding-right: 20px;">
                      <div class="count-title"><u>PERCENTAGE</u></div>
                      <div class="count-title" style="margin-top: 10px; color: #001ebf; font-size: 1rem; font-weight: bold;">Max</div>
                      <div class="count-title2" style="margin-top: 1px;">{{item.surveyData.measurePercentageMax}} %</div>
                      <div class="count-title" style="margin-top: 3px; color: #001ebf; font-size: 1rem; font-weight: bold;">Min</div>
                      <div class="count-title2" style="margin-top: 1px;">{{item.surveyData.measurePercentageMin}} %</div>
                    </td>
                    <td style="vertical-align: top; padding-left: 20px;">
                      <div class="count-title"><u>POINT AVERAGE</u></div>
                      <div class="count-title" style="margin-top: 10px; color: #001ebf; font-size: 1rem; font-weight: bold;">Max</div>
                      <div class="count-title2" style="margin-top: 1px;">{{item.surveyData.measureAverageMax}} #</div>
                      <div class="count-title" style="margin-top: 3px; color: #001ebf; font-size: 1rem; font-weight: bold;">Min</div>
                      <div class="count-title2" style="margin-top: 1px;">{{item.surveyData.measureAverageMin}} #</div>
                    </td>
                  </tr>
                </table>
              </div>
              
              
              
              </div>
              <div class="count-container"  style="min-width: 1390px;margin-bottom: 1rem;">  
          <!--    <div    class="count-box"  (click)="showModalDetails('MeasureAverage')">
                  <div class="count-title">Punteggio Medio Max: </div>
                  <div class="count-title">Punteggio Medio Min: </div>
                  <div class="count-title space"></div>
                  <div class="count-title space"></div>
                  <div class="count-title space"></div>
                  <div class="count-title space"></div>               
                  <div class="count-title">2024: </div>
                </div>-->  
                <!--  <div   class="count-box"  (click)="showModalDetails('MeasureTop')"> --> 
     
                  <div class="count-box2">
                    <div class="count-title"><u>TOP 5 CONTRACTS - PERCENTAGE</u></div>

                    <div class="count-title" style="margin-top:  0rem;">
                      <div 
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.contract_nameTh1 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:   -0.2rem;;">
                        {{ item?.surveyData?.threshold_resultTh1 }} %
                      </div>
                    </div>                 
                    <div class="count-title" style="margin-top:  -0.2rem;">
                      <div 
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.contract_nameTh2 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:  -0.2rem;;">
                        {{ item?.surveyData?.threshold_resultTh2 }} %
                      </div>
                    </div>
                    <div class="count-title" style="margin-top:  -0.2rem;;">
                      <div 
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.contract_nameTh3 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:  -0.2rem;;">
                        {{ item?.surveyData?.threshold_resultTh3 }} %
                      </div>
                    </div>
                    <div class="count-title" style="margin-top:  -0.2rem;;">
                      <div
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.contract_nameTh4 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:  -0.2rem;;">
                        {{ item?.surveyData?.threshold_resultTh4 }} %
                      </div>
                    </div>
                    <div class="count-title" style="margin-top:  -0.2rem;;">
                      <div
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.contract_nameTh5 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:  -0.2rem;;">
                        {{ item?.surveyData?.threshold_resultTh5 }} %
                      </div>
                    </div>
                  
                  </div>
                  
                  
                <!--  <div  class="count-box"   (click)="showModalDetails('MeasureWrost')"> --> 
                  <div class="count-box2">
                    <div class="count-title"><u>BOTTOM 5 CONTRACTS - PERCENTAGE</u></div>
            
                    <div class="count-title" style="margin-top: 0rem;">
                      <div 
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.bottom_contract_nameTh1 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:  -0.2rem;;">
                        {{ item?.surveyData?.bottom_threshold_resultTh1 }} %
                      </div>
                    </div>
                  
                    <div class="count-title" style="margin-top:  -0.2rem;;">
                      <div 
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.bottom_contract_nameTh2 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:  -0.2rem;;">
                        {{ item?.surveyData?.bottom_threshold_resultTh2 }} %
                      </div>
                    </div>
                  
                    <div class="count-title" style="margin-top:  -0.2rem;;">
                      <div 
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.bottom_contract_nameTh3 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:  -0.2rem;;">
                        {{ item?.surveyData?.bottom_threshold_resultTh3 }} %
                      </div>
                    </div>
                  
                    <div class="count-title" style="margin-top:  -0.2rem;;">
                      <div 
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.bottom_contract_nameTh4 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:  -0.2rem;;">
                        {{ item?.surveyData?.bottom_threshold_resultTh4 }} %
                      </div>
                    </div>
                    <div class="count-title" style="margin-top:  -0.2rem;;">
                      <div
                           data-container="body">
                        <span class="truncateContract">{{ item?.surveyData?.bottom_contract_nameTh5 }}</span>
                      </div>
                      <div class="count-title2" style="margin-top:  -0.2rem;;">
                        {{ item?.surveyData?.bottom_threshold_resultTh5 }} %
                      </div>
                    </div>
                 
                  </div>
                                     
                 <!--   <div class="count-title">{{ item.surveyData.bottom_contract_name1}} / {{ item.surveyData.bottom_average_result1}} %</div>
                    <div class="count-title">{{ item.surveyData.bottom_contract_name2}} / {{ item.surveyData.bottom_average_result2}} %</div>
                    <div class="count-title">{{ item.surveyData.bottom_contract_name3}} / {{ item.surveyData.bottom_average_result3}} %</div>
                    <div class="count-title">{{ item.surveyData.bottom_contract_name4}} / {{ item.surveyData.bottom_average_result4}} %</div>
                    <div class="count-title">{{ item.surveyData.bottom_contract_name5}} / {{ item.surveyData.bottom_average_result5}} %</div>
                -->
              </div>
              <div class="count-container"  style="min-width: 1390px;margin-bottom: 1rem">        
  
                <div class="count-box2">
                  <div class="count-title"><u>TOP 5 CONTRACTS - AVERAGE</u></div>
                
                  <div class="count-title" style="margin-top:  0rem;;">
                    <div 
                     data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.contract_name1 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.average_result1 }} #
                    </div>
                  </div>
                
                  <div class="count-title" style="margin-top:  -0.2rem;;">
                    <div 
                    data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.contract_name2 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.average_result2 }} #
                    </div>
                  </div>
                
                  <div class="count-title" style="margin-top:  -0.2rem;;">
                    <div 
                    data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.contract_name3 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.average_result3 }} #
                    </div>
                  </div>
                
                  <div class="count-title" style="margin-top:  -0.2rem;;">
                    <div 
                    data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.contract_name4 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.average_result4 }} #
                    </div>
                  </div>
                
                  <div class="count-title" style="margin-top:  -0.2rem;;">
                    <div 
                    data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.contract_name5 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.average_result5 }} #
                    </div>
                  </div>
                </div>
                
               <!--  <div  class="count-box"   (click)="showModalDetails('MeasureWrost')">--> 
                <div class="count-box2">
                  <div class="count-title"><u>BOTTOM 5 CONTRACTS - AVERAGE</u></div>
                
                  <div class="count-title" style="margin-top:  0rem;;">
                    <div 
                    data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.bottom_contract_name1 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.bottom_average_result1 }} #
                    </div>
                  </div>
                
                  <div class="count-title" style="margin-top:  -0.2rem;;">
                    <div 
                     data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.bottom_contract_name2 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.bottom_average_result2 }} #
                    </div>
                  </div>
                
                  <div class="count-title" style="margin-top:  -0.2rem;;">
                    <div 
                    data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.bottom_contract_name3 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.bottom_average_result3 }} #
                    </div>
                  </div>
                
                  <div class="count-title" style="margin-top:  -0.2rem;;">
                    <div 
                    data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.bottom_contract_name4 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.bottom_average_result4 }} #
                    </div>
                  </div>
                
                  <div class="count-title" style="margin-top:  -0.2rem;;">
                    <div 
                     data-container="body">
                      <span class="truncateContract">
                        {{ item?.surveyData?.bottom_contract_name5 }}
                      </span>
                    </div>
                    <div class="count-title2" style="margin-top:  -0.2rem;;">
                      {{ item?.surveyData?.bottom_average_result5 }} #
                    </div>
                  </div>
                </div>
                
          <!--    <div    class="count-box"  (click)="showModalDetails('MeasureAverage')">
                  <div class="count-title">Punteggio Medio Max: </div>
                  <div class="count-title">Punteggio Medio Min: </div>
                  <div class="count-title space"></div>
                  <div class="count-title space"></div>
                  <div class="count-title space"></div>
                  <div class="count-title space"></div>               
                  <div class="count-title">2024: </div>
                </div>-->  
              <!--    <div   class="count-box"  (click)="showModalDetails('MeasureTop')">-->                       
                </div>             
              </div>
              </div>
           </div>
          </div>
        </div>
        </div>

</div>
<!--  END SURVEY-->



<!-- Ticket WF Ticket -->

<div *ngIf="item.widgetDetails.Type == 'ticketCount'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'ticketCount'">
  <div  style=" min-height: 500px; min-width: 1px; top: 5px" echarts [options]="item.data" class="mt-4"></div>                     
  </div>
  <div *ngIf="item.widgetDetails.Type == 'ticketClosed'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'ticketClosed'">
    <div  style=" min-height: 500px; min-width: 1px; top: 5px" echarts [options]="item.data" class="mt-4"></div>                     
    </div>
  
    <div *ngIf="item.widgetDetails.Type == 'ticketRate'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'ticketRate'">
      <div  style=" min-height: 500px; min-width: 1px; top: 5px" echarts [options]="item.data" class="mt-4"></div>                     
      </div>
    

<!-- End WF Ticket-->



<!--  Trend contract-->
<div *ngIf="item.widgetDetails.Type == 'calculationContractAll' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractAll' && item.widgetData.chartRecord != 'chart'">
  <div  style=" min-height: 500px; min-width: 1px; top: 4px" echarts [options]="item.data" class="mt-4"></div>                     
  </div>
        <div *ngIf="item.widgetDetails.Type == 'calculationContractMonth' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractMonth' && item.widgetData.chartRecord != 'chart'">
       
         <!-- <div *ngIf="item.byContractMessage != '' " class="box-header with-border" style="padding-top: 20px;text-align: center;">
            <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>{{item.byContractMessage}}</b></p>
            </div>-->
          <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" (chartClick)="onChartClick($event, item)" class="mt-4"></div>                     
        </div>
        <div *ngIf="item.widgetDetails.Type == 'calculationContractWeek' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractWeek' && item.widgetData.chartRecord != 'chart'">
          <!--   <div *ngIf="item.byContractMessage != '' " class="box-header with-border" style="padding-top: 20px;text-align: center;">          
            <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>{{item.byContractMessage}}</b></p>
            </div>-->
          <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data"  (chartClick)="onChartClick($event, item)" class="mt-4"></div>                     
          </div>
        <div *ngIf="item.widgetDetails.Type == 'calculationContractDays' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractDays' && item.widgetData.chartRecord != 'chart'">
          <!-- <div *ngIf="item.byContractMessage != '' " class="box-header with-border" style="padding-top: 20px;text-align: center;">        
            <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>{{item.byContractMessage}}</b></p>
            </div>-->
          <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" (chartClick)="onChartClick($event, item)" class="mt-4"></div>                     
        </div>     
        <div *ngIf="item.widgetDetails.Type == 'calculationContractSemester' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractSemester' && item.widgetData.chartRecord != 'chart'">
          <!--<div *ngIf="item.byContractMessage != '' " class="box-header with-border" style="padding-top: 20px;text-align: center;">       
            <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>{{item.byContractMessage}}</b></p>
            </div>-->
          <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" (chartClick)="onChartClick($event, item)" class="mt-4"></div>                     
        </div>       
        <div *ngIf="item.widgetDetails.Type == 'calculationContractQuarter' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractQuarter' && item.widgetData.chartRecord != 'chart'">
          <!-- <div *ngIf="item.byContractMessage != '' " class="box-header with-border" style="padding-top: 20px;text-align: center;">          
            <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>{{item.byContractMessage}}</b></p>
            </div>-->
          <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" (chartClick)="onChartClick($event, item)" class="mt-4"></div>                     
        </div>      
        <div *ngIf="item.widgetDetails.Type == 'calculationContractYear' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractYear' && item.widgetData.chartRecord != 'chart'">
        <!--  <div *ngIf="item.byContractMessage != '' " class="box-header with-border" style="padding-top: 20px;text-align: center;">
            <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>{{item.byContractMessage}}</b></p>
            </div>-->
          <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" (chartClick)="onChartClick($event, item)" class="mt-4"></div>                     
        </div>
<!-- End  Trend-->

<!-- record Trend contract-->
<div *ngIf="item.widgetDetails.Type == 'calculationContractAll' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractAll' && item.widgetData.chartRecord != 'record'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
   <!-- <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  -->
   <!-- <th pSortableColumn="serieCompliantDay">Compliant Day <p-sortIcon field="serieCompliantDay"></p-sortIcon></th> 
    <th pSortableColumn="serieCompliantMonth">Compliant Month<p-sortIcon field="serieCompliantMonth"></p-sortIcon></th> 
    <th pSortableColumn="serieCompliantQuarter">Compliant Quarter<p-sortIcon field="serieCompliantQuarter"></p-sortIcon></th>        
    <th pSortableColumn="serieCompliantHalfYear">Compliant Half-year<p-sortIcon field="serieCompliantHalfYear"></p-sortIcon></th>      
    <th pSortableColumn="serieCompliantYear">Compliant Year<p-sortIcon field="serieCompliantYear"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliantDay">Not Compliant Day <p-sortIcon field="serieNotCompliantDay"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliantMonth">Not Compliant Month<p-sortIcon field="serieNotCompliantMonth"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliantQuarter">Not Compliant Quarter<p-sortIcon field="serieNotCompliantQuarter"></p-sortIcon></th>        
    <th pSortableColumn="serieNotCompliantHalfYear">Not Compliant Half-year<p-sortIcon field="serieNotCompliantHalfYear"></p-sortIcon></th>      
    <th pSortableColumn="serieNotCompliantYear">Not Compliant Year<p-sortIcon field="serieNotCompliantYear"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCalculatedDay">Not Calculated Day <p-sortIcon field="serieNotCalculatedDay"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCalculatedMonth">Not Calculated Month<p-sortIcon field="serieNotCalculatedMonth"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCalculatedQuarter">Not Calculated Quarter<p-sortIcon field="serieNotCalculatedQuarter"></p-sortIcon></th>        
    <th pSortableColumn="serieNotCalculatedHalfYear">Not Calculated Half-year<p-sortIcon field="serieNotCalculatedHalfYear"></p-sortIcon></th>      
    <th pSortableColumn="serieNotCalculatedYear">Not Calculated Year<p-sortIcon field="serieNotCalculatedYear"></p-sortIcon></th> -->
    <th pSortableColumn="serieName">calculation Summary <p-sortIcon field="serieName"></p-sortIcon></th>
    <th pSortableColumn="value">Value <p-sortIcon field="value"></p-sortIcon></th>
         
 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal"  [tooltip]= "row.serieName">{{ row.serieName }}</td>
        <td style="text-align:center"  class="truncateModal"  [tooltip]= "row.value + '%'"  >{{ row.value }}{{'%'}}</td>
       <!-- <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>      --> 

      <!--  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantDay + '%'" data-container="body">{{row.serieCompliantDay}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantMonth + '%'" data-container="body">{{row.serieCompliantMonth}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantQuarter + '%'" data-container="body">{{row.serieCompliantQuarter}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantHalfYear + '%'" data-container="body">{{row.serieCompliantHalfYear}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantYear + '%'" data-container="body">{{row.serieCompliantYear}}{{'%'}}</td> 

        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantDay + '%'" data-container="body">{{row.serieNotCompliantDay}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantMonth + '%'" data-container="body">{{row.serieNotCompliantMonth}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantQuarter + '%'" data-container="body">{{row.serieNotCompliantQuarter}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantHalfYear + '%'" data-container="body">{{row.serieNotCompliantHalfYear}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantYear + '%'" data-container="body">{{row.serieNotCompliantYear}}{{'%'}}</td> 

        
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedDay + '%'" data-container="body">{{row.serieNotCalculatedDay}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedMonth + '%'" data-container="body">{{row.serieNotCalculatedMonth}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedQuarter + '%'" data-container="body">{{row.serieNotCalculatedQuarter}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedHalfYear + '%'" data-container="body">{{row.serieNotCalculatedHalfYear}}{{'%'}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedYear + '%'" data-container="body">{{row.serieNotCalculatedYear}}{{'%'}}</td> 
    -->  </tr>
    </ng-template>
        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="2">No data</td>
  </ng-template>

 </p-table>
  </div>
<div *ngIf="item.widgetDetails.Type == 'calculationContractMonth' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractMonth' && item.widgetData.chartRecord != 'record'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
    <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
    <th pSortableColumn="serieNotCalculated">Others<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>       
    <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>   
    <th pSortableColumn="serienotcalculatedTotal">Not calculated <p-sortIcon field="serienotcalculatedTotal"></p-sortIcon></th>             
 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>                   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td> 
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serienotcalculatedTotal" data-container="body">{{row.serienotcalculatedTotal}}</td>    
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
  </div>
  <div *ngIf="item.widgetDetails.Type == 'calculationContractWeek' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractWeek' && item.widgetData.chartRecord != 'record'">
             
    <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
    [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
    <ng-template pTemplate="caption">
      <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
        <div>
          <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
          Count: {{item.countRecordWidget}} 
        </div>
          <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
            <div class="input-group">
              <div class="input-group-prepend">
              <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
              </div>
              <div  class="gridster-item-content">
              <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
          </div>  </div>
          </div>
      </div>
    </ng-template>
    <ng-template pTemplate="header" let-row let-columns >
    <tr class="fuseqTableHeader" style="white-space: nowrap;">
      <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
      <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
      <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
      <th pSortableColumn="serieNotCalculated">Others<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>           
      <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>   
    <th pSortableColumn="serienotcalculatedTotal">Not calculated <p-sortIcon field="serienotcalculatedTotal"></p-sortIcon></th>          
   
    </tr>
    <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
     <th  *ngFor="let col of columns" style="background-color: white !important">
      <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
       style=" text-align:center"  colpsan= "14"
        placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
  
        [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
        type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
      </th>
    </tr>
  </ng-template>
      <ng-template pTemplate="body" let-row  let-i="index">
        <tr >
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>                   
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>   
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serienotcalculatedTotal" data-container="body">{{row.serienotcalculatedTotal}}</td>   
        </tr>
      </ng-template>
  <!--        <ng-template pTemplate="emptymessage">
          <td style="text-align:center" colspan="10">No data</td>
    </ng-template>--> 
  
   </p-table>
    </div>
<div *ngIf="item.widgetDetails.Type == 'calculationContractQuarter' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractQuarter' && item.widgetData.chartRecord != 'record'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
    <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
    <th pSortableColumn="serieNotCalculated">Others<p-sortIcon field="serieNotCalculated"></p-sortIcon></th> 
    <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>   
    <th pSortableColumn="serienotcalculatedTotal">Not calculated <p-sortIcon field="serienotcalculatedTotal"></p-sortIcon></th>                    
 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>                   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serienotcalculatedTotal" data-container="body">{{row.serienotcalculatedTotal}}</td>  
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
  </div> 
<div *ngIf="item.widgetDetails.Type == 'calculationContractSemester' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractSemester' && item.widgetData.chartRecord != 'record'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
    <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
    <th pSortableColumn="serieNotCalculated">Others<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>    
    <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>   
    <th pSortableColumn="serienotcalculatedTotal">Not calculated <p-sortIcon field="serienotcalculatedTotal"></p-sortIcon></th>                 
 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>                   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td> 
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serienotcalculatedTotal" data-container="body">{{row.serienotcalculatedTotal}}</td>   
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
  </div>
<div *ngIf="item.widgetDetails.Type == 'calculationContractYear' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractYear' && item.widgetData.chartRecord != 'record'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
    <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
    <th pSortableColumn="serieNotCalculated">Others<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>              
    <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>   
    <th pSortableColumn="serienotcalculatedTotal">Not calculated <p-sortIcon field="serienotcalculatedTotal"></p-sortIcon></th>       
 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>                   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serienotcalculatedTotal" data-container="body">{{row.serienotcalculatedTotal}}</td> 
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
  </div>
<div *ngIf="item.widgetDetails.Type == 'calculationContractDays' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractDays' && item.widgetData.chartRecord != 'record'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
    <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
    <th pSortableColumn="serieNotCalculated">Others<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>   
    <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>   
    <th pSortableColumn="serienotcalculatedTotal">Not calculated <p-sortIcon field="serienotcalculatedTotal"></p-sortIcon></th>                  
 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>                   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serienotcalculatedTotal" data-container="body">{{row.serienotcalculatedTotal}}</td> 
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
  </div>
<!--  end record Trend contract-->



<!-- record Trend customer-->
<div *ngIf="item.widgetDetails.Type == 'calculationCustomerWeek' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationCustomerWeek' && item.widgetData.chartRecord != 'record'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="nameYaxis">Contract<p-sortIcon field="nameYaxis"></p-sortIcon></th>  
    <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
    <th pSortableColumn="serieNotCalculated">Not calculated<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>       
    <th pSortableColumn="serieOthers">Others<p-sortIcon field="serieOthers"></p-sortIcon></th>                    
    <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>               
 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateWidget" [tooltip]= "row.nameYaxis"  data-container="body">{{row.nameYaxis}}</td>                   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td> 
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieOthers" data-container="body">{{row.serieOthers}}</td>    
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>  
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
  </div>

<div *ngIf="item.widgetDetails.Type == 'calculationCustomerMonth' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractMonth' && item.widgetData.chartRecord != 'record'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="nameYaxis">Contract<p-sortIcon field="nameYaxis"></p-sortIcon></th>  
    <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
    <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
    <th pSortableColumn="serieNotCalculated">Not calculated<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>
    <th pSortableColumn="serieOthers">Others<p-sortIcon field="serieOthers"></p-sortIcon></th>                    
    <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>                    
 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateWidget" [tooltip]= "row.nameYaxis"  data-container="body">{{row.nameYaxis}}</td>                   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>   
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieOthers" data-container="body">{{row.serieOthers}}</td>  
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>  

      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
  </div>
  <div *ngIf="item.widgetDetails.Type == 'calculationCustomerDays' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractDays' && item.widgetData.chartRecord != 'record'">
             
    <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
    [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
    <ng-template pTemplate="caption">
      <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
        <div>
          <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
          Count: {{item.countRecordWidget}} 
        </div>
          <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
            <div class="input-group">
              <div class="input-group-prepend">
              <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
              </div>
              <div  class="gridster-item-content">
              <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
          </div>  </div>
          </div>
      </div>
    </ng-template>
    <ng-template pTemplate="header" let-row let-columns >
    <tr class="fuseqTableHeader" style="white-space: nowrap;">
      <th pSortableColumn="nameYaxis">Contract<p-sortIcon field="nameYaxis"></p-sortIcon></th>  
      <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
      <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
      <th pSortableColumn="serieNotCalculated">Not calculated<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>
      <th pSortableColumn="serieOthers">Others<p-sortIcon field="serieOthers"></p-sortIcon></th>                    
      <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>                    
   
    </tr>
    <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
     <th  *ngFor="let col of columns" style="background-color: white !important">
      <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
       style=" text-align:center"  colpsan= "14"
        placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
  
        [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
        type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
      </th>
    </tr>
  </ng-template>
      <ng-template pTemplate="body" let-row  let-i="index">
        <tr >
          <td style="text-align:center"  class="truncateWidget" [tooltip]= "row.nameYaxis"  data-container="body">{{row.nameYaxis}}</td>                   
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>  
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieOthers" data-container="body">{{row.serieOthers}}</td>  
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
        </tr>
      </ng-template>
  <!--        <ng-template pTemplate="emptymessage">
          <td style="text-align:center" colspan="10">No data</td>
    </ng-template>--> 
  
   </p-table>
    </div>
  
  <!---------BUsiness REQUEST--------------->

 <div *ngIf="item.widgetDetails.Type == 'typeAss'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'typeAss'">
      <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
      </div>
      <div *ngIf="item.widgetDetails.Type == 'category'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'category'">
        <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" (chartClick)="onChartClickPP($event)" class="mt-4"></div>                     
          </div>
          <div *ngIf="item.widgetDetails.Type == 'contractParty'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'contractParty'">
              <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data"  class="mt-4"></div>                     
              </div>
   <div class="col sm-12" id="countTot" style="display: contents;" [hidden]="isSaved == false || item.widgetDetails.Type != 'countTot'">
              <div class="box-header with-border" style="padding-top: 0px;text-align: center;"><p>Summary</p></div>

          <div class="row" style="margin-left: unset;margin-right: unset;">
            <div class="col sm-3">
              <div class="row justify-content-center">Total Request</div>
              <div class="row justify-content-center">
              
              <circle-progress class="clickable" (click)="reqall.all > 0 && showModalDetailsCounter('all')"
                  [percent]=reqall.all
                  [radius]="40"
                  [subtitleFontSize]="10"
                  [titleFontSize]="14"
                  [showSubtitle]="false"
                  [unitsFontSize]="10"
                  [outerStrokeWidth]="8"
                  [innerStrokeWidth]="5"
                  [innerStrokeColor]="'#2196f3'"
                  [outerStrokeColor] ="'#fe9800'"
                  [titleFontWeight]="600"
                  [animation]="true"
                  [animationDuration]="0"
                  [showUnits] = false
                  [outerStrokeColor] = "'#00000'"
                ></circle-progress>
                </div>
             </div>
             <div class="col sm-3 justify-content-center">
             <div class="row justify-content-center">Expired</div>
             <div class="row justify-content-center">
              <circle-progress class="clickable" (click)="reqall.expired > 0 && showModalDetailsCounter('expired')"
                  [percent]=reqall.expired
                  [radius]="40"
                  [subtitleFontSize]="10"
                  [titleFontSize]="14"
                  [showSubtitle]="false"
                  [unitsFontSize]="10"
                  [outerStrokeWidth]="8"
                  [innerStrokeWidth]="5"
                  [innerStrokeColor]="'red'"
                  [outerStrokeColor] ="'#fe9800'"
                  [titleFontWeight]="600"
                  [animation]="true"
                  [animationDuration]="0"
                  [showUnits] = false
                  [outerStrokeColor] = "'#00000'"
                ></circle-progress>
              </div>
          </div>
             
             <div class="col sm-3">
              <div class="row justify-content-center">To do</div>
              <div class="row justify-content-center">
              <circle-progress class="clickable" (click)="reqall.todo > 0 && showModalDetailsCounter('todo')"
                  [percent]=reqall.todo
                  [radius]="40"
                  [subtitleFontSize]="10"
                  [titleFontSize]="14"
                  [showSubtitle]="false"
                  [unitsFontSize]="10"
                  [outerStrokeWidth]="8"
                  [innerStrokeWidth]="5"
                  [innerStrokeColor]="'#ffd700'"
                  [outerStrokeColor] ="'#fe9800'"
                  [titleFontWeight]="600"
                  [animation]="true"
                  [animationDuration]="0"
                  [showUnits] = false
                  [outerStrokeColor] = "'#00000'"
                ></circle-progress>
             </div>
          </div>
             <div class="col sm-3">
              <div class="row justify-content-center">Send</div>
              <div class="row justify-content-center">
              
              <circle-progress class="clickable" (click)="reqall.sent > 0 && showModalDetailsCounter('sent')"
                  [percent]=reqall.sent
                  [radius]="40"
                  [subtitleFontSize]="10"
                  [titleFontSize]="14"
                  [showSubtitle]="false"
                  [unitsFontSize]="10"
                  [outerStrokeWidth]="8"
                  [innerStrokeWidth]="5"
                  [innerStrokeColor]="'#009688'"
                  [outerStrokeColor] ="'#fe9800'"
                  [titleFontWeight]="600"
                  [animation]="true"
                  [animationDuration]="0"
                  [showUnits] = false
                  [outerStrokeColor] = "'#00000'"
                ></circle-progress>
                </div>
             </div>
          </div>
              
             
              </div>


<!-----------------END BUSINESS------------------------------->



    <div *ngIf="item.widgetDetails.Type == 'calculationCustomerQuarter' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractQuarter' && item.widgetData.chartRecord != 'record'">
             
      <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
      [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
      <ng-template pTemplate="caption">
        <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
          <div>
            <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
            Count: {{item.countRecordWidget}} 
          </div>
            <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
              <div class="input-group">
                <div class="input-group-prepend">
                <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                </div>
                <div  class="gridster-item-content">
                <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
            </div>  </div>
            </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header" let-row let-columns >
      <tr class="fuseqTableHeader" style="white-space: nowrap;">
        <th pSortableColumn="nameYaxis">Contract<p-sortIcon field="nameYaxis"></p-sortIcon></th>  
        <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
        <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
        <th pSortableColumn="serieNotCalculated">Not calculated<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>
        <th pSortableColumn="serieOthers">Others<p-sortIcon field="serieOthers"></p-sortIcon></th>                    
        <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>                     
     
      </tr>
      <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
       <th  *ngFor="let col of columns" style="background-color: white !important">
        <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
         style=" text-align:center"  colpsan= "14"
          placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
    
          [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
          type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
        </th>
      </tr>
    </ng-template>
        <ng-template pTemplate="body" let-row  let-i="index">
          <tr >
            <td style="text-align:center"  class="truncateWidget" [tooltip]= "row.nameYaxis"  data-container="body">{{row.nameYaxis}}</td>                   
            <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
            <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
            <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>  
            <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieOthers" data-container="body">{{row.serieOthers}}</td>  
            <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
          </tr>
        </ng-template>
    <!--        <ng-template pTemplate="emptymessage">
            <td style="text-align:center" colspan="10">No data</td>
      </ng-template>--> 
    
     </p-table>
      </div>
      <div *ngIf="item.widgetDetails.Type == 'calculationCustomerSemester' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractSemester' && item.widgetData.chartRecord != 'record'">
             
        <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
        [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
        <ng-template pTemplate="caption">
          <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
            <div>
              <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
              Count: {{item.countRecordWidget}} 
            </div>
              <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                <div class="input-group">
                  <div class="input-group-prepend">
                  <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                  </div>
                  <div  class="gridster-item-content">
                  <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
              </div>  </div>
              </div>
          </div>
        </ng-template>
        <ng-template pTemplate="header" let-row let-columns >
        <tr class="fuseqTableHeader" style="white-space: nowrap;">
          <th pSortableColumn="nameYaxis">Contract<p-sortIcon field="nameYaxis"></p-sortIcon></th>  
          <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
          <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
          <th pSortableColumn="serieNotCalculated">Not calculated<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>
          <th pSortableColumn="serieOthers">Others<p-sortIcon field="serieOthers"></p-sortIcon></th>                    
          <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>                    
       
        </tr>
        <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
         <th  *ngFor="let col of columns" style="background-color: white !important">
          <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
           style=" text-align:center"  colpsan= "14"
            placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
      
            [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
            type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
          </th>
        </tr>
      </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >
              <td style="text-align:center"  class="truncateWidget" [tooltip]= "row.nameYaxis"  data-container="body">{{row.nameYaxis}}</td>                   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td> 
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieOthers" data-container="body">{{row.serieOthers}}</td>  
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>    
            </tr>
          </ng-template>
      <!--        <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="10">No data</td>
        </ng-template>--> 
      
       </p-table>
        </div>
      
        <div *ngIf="item.widgetDetails.Type == 'calculationCustomerYear' && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationContractYear' && item.widgetData.chartRecord != 'record'">
             
          <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
          [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
                Count: {{item.countRecordWidget}} 
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                    <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                    </div>
                    <div  class="gridster-item-content">
                    <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
                </div>  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-row let-columns >
          <tr class="fuseqTableHeader" style="white-space: nowrap;">
            <th pSortableColumn="nameYaxis">Contract<p-sortIcon field="nameYaxis"></p-sortIcon></th>  
            <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliantt"></p-sortIcon></th> 
            <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
            <th pSortableColumn="serieNotCalculated">Not calculated<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>
            <th pSortableColumn="serieOthers">Others<p-sortIcon field="serieOthers"></p-sortIcon></th>                    
            <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>          
         
          </tr>
          <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
           <th  *ngFor="let col of columns" style="background-color: white !important">
            <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
             style=" text-align:center"  colpsan= "14"
              placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
        
              [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
              type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
            </th>
          </tr>
        </ng-template>
            <ng-template pTemplate="body" let-row  let-i="index">
              <tr >
                <td style="text-align:center"  class="truncateWidget" [tooltip]= "row.nameYaxis"  data-container="body">{{row.nameYaxis}}</td>                   
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>   
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieOthers" data-container="body">{{row.serieOthers}}</td>  
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>  
              </tr>
            </ng-template>
        <!--        <ng-template pTemplate="emptymessage">
                <td style="text-align:center" colspan="10">No data</td>
          </ng-template>--> 
        
         </p-table>
          </div>
        

<!--  end record Trend customer-->

<!--  EXPIRATION-->
<div *ngIf="item.widgetDetails.Type == 'bySupplierContract'  && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'bySupplierContract' && item.widgetData.chartRecord != 'record'">

  <p-table  #pt class="pt custom-scroll-table"  [columns]="item.colsTableSummary" [value]="item.tableData"  [scrollable]="true" scrollHeight="flex"  sortMode="single" dataKey="customer_name"  [(expandedRowKeys)]="item.expandedRowKeys" rowGroupMode="subheader" groupRowsBy="customer_name" [globalFilterFields]="['customer_name','contract_names','period']">
   <ng-template pTemplate="caption">
   <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">          
       <div>    
         <button class="btn btn-outline-dark ml-2" (click)="toggleAllRows(item.tableData, item, item.expandedRowKeys)">            
           <i [class.pi-chevron-down]="item.areAllRowsExpanded" [class.pi-chevron-right]="!item.areAllRowsExpanded" class="pi" style="vertical-align: bottom;"></i>
           {{ item.areAllRowsExpanded ? 'Collapse All' : 'Expand All' }}
       </button>    
         <button class="btn btn-outline-dark ml-2" (click)="item.showColFilters = !item.showColFilters"><i *ngIf="item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="!item.showColFilters" class=" pi pi-filter"></i></button>           
         Contracts: {{pt.filteredValue?pt.filteredValue.length:pt.totalRecords}}
       </div>
       <div class="cold-md-6" style="text-align: right">
         <div class="form-group" style="margin-bottom:unset">
           <div class="input-group">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="pt.filterGlobal($event.target.value, 'contains'); pt.resetScrollTop()" >
            </div>
         </div>
       </div>
     </div>
   </ng-template>     
   <ng-template pTemplate="header">
         <tr>
             <th pSortableColumn="customer_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                 Contract Party<p-sortIcon field="customer_name"></p-sortIcon>
             </div></th>
             <th pSortableColumn="contract_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">
               Contract<p-sortIcon field="contract_name"></p-sortIcon>
             </div></th>
 
           <th pSortableColumn="metric" style="padding:unset;"><div class="flex justify-content-center align-items-center">
            Periods<p-sortIcon field="metric"></p-sortIcon>
        </div></th>    
        <th pSortableColumn="campaigns" style="padding:unset;"><div class="flex justify-content-center align-items-center">
          campaigns<p-sortIcon field="campaigns"></p-sortIcon>
      </div></th>      
         </tr>
         <tr *ngIf="item.showColFilters">
             <th style="padding:unset;background-color: white !important">
             <input style="padding: unset;font-size: 12px;margin: 0px; text-align: center;" type="search" required  id="customer_name" class="form-control" (input)="pt.filter($event.target.value, ['customer_name'] , 'contains')">     
             </th >
             <th style="padding:unset;background-color: white !important">
                 <input style="padding: unset;font-size: 12px;margin: 0px; text-align: center;" type="search" required  id="contract_name" class="form-control" (input)="pt.filter($event.target.value, ['contrac_name'] , 'contains')">     
             </th>
             <th style="padding:unset;background-color: white !important">
               <input style="padding: unset;font-size: 12px;margin: 0px; text-align: center;" type="search" required  id="metric" class="form-control" (input)="pt.filter($event.target.value, ['metric'] , 'contains')">
               </th>
               <th style="padding:unset;background-color: white !important">
                <input style="padding: unset;font-size: 12px;margin: 0px; text-align: center;" type="search" required  id="campaigns" class="form-control" (input)="pt.filter($event.target.value, ['campaigns'] , 'contains')">
                </th>
         </tr>
     </ng-template>
     <ng-template pTemplate="groupheader" let-customer let-rowIndex="rowIndex" let-expanded="expanded">
       <tr style="  background-color: lightgray !important;">
         <td colspan="7" style="vertical-align: middle;" >
 
   <button style="width: auto; height: 0.5rem" type="button" pButton pRipple [pRowToggler]="customer" class="p-button-text p-button-rounded p-button-plain mr-2" [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
               <span  class="truncateshowField" [tooltip]= "customer.customer_name"><b>{{customer.customer_name}}</b>  </span>
             </td>
         </tr>
     </ng-template>
     <ng-template pTemplate="rowexpansion" let-customer>
         <tr><td></td>                
             <td style="text-align: center;" [tooltip]= "customer.contract_names">{{customer.contract_name}}</td>
             <td style="text-align: center;" [tooltip]= "customer.metric"> {{customer.metric }}</td>      
             <td style="text-align: center;" [tooltip]= "customer.campaigns"> {{customer.campaigns }}</td>   
         </tr>
     </ng-template>
 </p-table>

</div>
<div *ngIf="((item.widgetDetails.Type == 'numberExpiringContract' || item.widgetDetails.Type == 'durationContract' || item.widgetDetails.Type =='violatedPeriods' || item.widgetDetails.Type =='violatedMetrics' ||  item.widgetDetails.Type =='financialPenalties') && item.widgetData.chartRecord == 'chart')" class="clickable sm-4"  style="display: contents;" [hidden]="((item.widgetDetails.Type  !== 'numberExpiringContract' || item.widgetDetails.Type !== 'durationContract' || item.widgetDetails.Type =='violatedPeriods' || item.widgetDetails.Type =='violatedMetrics' ||  item.widgetDetails.Type =='financialPenalties') && item.widgetData.chartRecord != 'chart')">
  <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" 
  (chartClick)="(item.widgetDetails.Type == 'violatedMetrics' || item.widgetDetails.Type == 'violatedPeriods' || item.widgetDetails.Type == 'financialPenalties') ? onChartClick1($event, item) : null"  class="mt-4"></div>                     
  </div>

<div *ngIf="item.widgetDetails.Type == 'compliantContract' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type  !== 'compliantContract'  && item.widgetData.chartRecord != 'chart'">
<div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
</div>

<!--<div *ngIf="item.widgetDetails.Type == 'listExpiringContract'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'listExpiringContract'">
  <p-table #pt [value]="item.tableDataExipring" [scrollable]="true" scrollHeight="flex"  sortMode="single" dataKey="customer_name"  [(expandedRowKeys)]="item.expandedRowKeys" rowGroupMode="subheader" groupRowsBy="customer_name" [globalFilterFields]="['customer_name','contract_names','period']">
    <ng-template pTemplate="caption">
  
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">          
        <div>    
          <button class="btn btn-outline-dark ml-2" (click)="toggleAllRows(item.tableDataExipring, item, item.expandedRowKeys)">             
            <i [class.pi-chevron-down]="item.areAllRowsExpanded" [class.pi-chevron-right]="!item.areAllRowsExpanded" class="pi" style="vertical-align: bottom;"></i>
            {{ item.areAllRowsExpanded ? 'Collapse All' : 'Expand All' }}
        </button>    
          <button class="btn btn-outline-dark ml-2" (click)="item.showColFilters = !item.showColFilters"><i *ngIf="item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="!item.showColFilters" class=" pi pi-filter"></i></button>           
          Contracts: {{item.countRecordWidget2}} 
        </div>
        <div class="cold-md-6" style="text-align: right">
          <div class="form-group" style="margin-bottom:unset">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text" style="min-width: 95px;">Global Search</span>
              </div>
              <input type="search" class="form-control" pInputText size="30" (input)="pt.filterGlobal($event.target.value, 'contains'); pt.resetScrollTop()" style="width:auto">      
            </div>
          </div>
        </div>
      </div>
    </ng-template>     
    <ng-template pTemplate="header">
          <tr>
              <th pSortableColumn="customer_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                  Contract Party<p-sortIcon field="customer_name"></p-sortIcon>
              </div></th>
              <th pSortableColumn="contract_names" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                Contract<p-sortIcon field="contract_name"></p-sortIcon>
              </div></th>
              <th pSortableColumn="period" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                Period<p-sortIcon field="period"></p-sortIcon>
            </div></th>      
          </tr>
          <tr *ngIf="item.showColFilters">
              <th style="padding:unset;background-color: white !important">
              <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="customer_name" class="form-control" (input)="pt.filter($event.target.value, ['customer_name'] , 'contains')">     
              </th >
              <th style="padding:unset;background-color: white !important">
                  <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="contract_names" class="form-control" (input)="pt.filter($event.target.value, ['contract_names'] , 'contains')">     
              </th>
              <th style="padding:unset;background-color: white !important">
                <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="period" class="form-control" (input)="pt.filter($event.target.value, ['period'] , 'contains')">
                </th>
          </tr>
      </ng-template>
      <ng-template pTemplate="groupheader" let-customer let-rowIndex="rowIndex" let-expanded="expanded">
        <tr style="  background-color: lightgray !important;">
          <td colspan="7" style="vertical-align: middle;" >
  
    <button style="width: auto; height: 0.5rem" type="button" pButton pRipple [pRowToggler]="customer" class="p-button-text p-button-rounded p-button-plain mr-2" [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                <span  class="truncateshowField" [tooltip]= "customer.customer_name"><b>{{customer.customer_name}}</b>  </span>
              </td>
          </tr>
      </ng-template>
      <ng-template pTemplate="rowexpansion" let-customer>
          <tr>
              <td></td>                
              <td style="text-align: center;" [tooltip]= "customer.contract_names">{{customer.contract_names}}</td>
              <td style="text-align: center;" [tooltip]= "customer.period"> {{customer.period }}</td>           
          </tr>
      </ng-template>
  </p-table>
</div>-->

<div *ngIf="(item.widgetDetails.Type == 'durationContract' || item.widgetDetails.Type == 'compliantContract')  && item.widgetData.chartRecord == 'record'"  class="clickable col-md-6"  style="display: contents;" [hidden]="(item.widgetDetails.Type != 'durationContract'|| item.widgetDetails.Type == 'compliantContract')  && item.widgetData.chartRecord != 'record'">
 
<p-table [columns]="item.colsTableExipiring"  #pt class="pt custom-scroll-table"  [value]="item.tableData" scrollHeight="flex" #indicatorsTable 
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{pt.filteredValue?pt.filteredValue.length:pt.totalRecords}}
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="pt.filterGlobal($event.target.value, 'contains'); pt.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="seriesLabel">{{ item.widgetDetails.Type === 'durationContract'  ? 'Contract' : 'Period' }}<p-sortIcon field="seriesLabel"></p-sortIcon></th>  
    <th  [hidden]= "item.widgetDetails.Type !== 'compliantContract'" pSortableColumn="seriesCompliant">Compliant Contracts<p-sortIcon field="seriesCompliant"></p-sortIcon></th>
   <th pSortableColumn="seriesCount">{{ item.widgetDetails.Type === 'durationContract'  ? 'Month' : 'Total Contracts' }}<p-sortIcon field="seriesCount"></p-sortIcon></th> 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important"  [hidden]= "item.widgetDetails.Type !== 'compliantContract' && col.field == 'seriesCompliant'">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style="text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriesLabel"  data-container="body">{{row.seriesLabel}}</td>  
        <td [hidden]= "item.widgetDetails.Type !== 'compliantContract'"  style="text-align:center"  class="truncateModal" [tooltip]= "row.seriesCompliant"  data-container="body">{{row.seriesCompliant}}</td>    
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriesCount"  data-container="body">{{row.seriesCount}}</td>  
        
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
</div>
<div *ngIf="((item.widgetDetails.Type == 'numberExpiringContract'  || item.widgetDetails.Type =='violatedMetrics' || item.widgetDetails.Type =='violatedPeriods'  || item.widgetDetails.Type =='financialPenalties'  )  && item.widgetData.chartRecord == 'record')"  class="clickable col-md-6"  style="display: contents;" [hidden]="((item.widgetDetails.Type != 'numberExpiringContract'  || item.widgetDetails.Type =='violatedMetrics' || item.widgetDetails.Type =='violatedPeriods' || item.widgetDetails.Type =='financialPenalties' ) && item.widgetData.chartRecord != 'record')">
  <tabset #tabCreate>
    <tab #tab1 *ngIf="item.widgetDetails.Type == 'violatedMetrics' ||  item.widgetDetails.Type =='violatedPeriods'" heading="By contract">  

      <p-table #pt  class="pt custom-scroll-table"  [columns]="item.colsTableExipiring2" [value]="item.tableDataExipring"  [scrollable]="true" scrollHeight="flex"  [sortMode]="'multiple'" dataKey="contract_name"  [(expandedRowKeys)]="item.expandedRowKeys" rowGroupMode="subheader" groupRowsBy="contract_name" [globalFilterFields]="['contract_name','period', 'metrics']">
       <ng-template pTemplate="caption">
       <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">          
           <div>    
             <button class="btn btn-outline-dark ml-2" (click)="toggleAllRows(item.tableDataExipring, item, item.expandedRowKeys)">            
               <i [class.pi-chevron-down]="item.areAllRowsExpanded" [class.pi-chevron-right]="!item.areAllRowsExpanded" class="pi" style="vertical-align: bottom;"></i>
               {{ item.areAllRowsExpanded ? 'Collapse All' : 'Expand All' }}
           </button>    
             <button class="btn btn-outline-dark ml-2" (click)="item.showColFilters = !item.showColFilters"><i *ngIf="item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="!item.showColFilters" class=" pi pi-filter"></i></button>           
           <!--  Contracts: {{item.countRecordWidget2}} -->
             Contracts:{{pt.filteredValue?pt.filteredValue.length:pt.totalRecords}}
           </div>
           <div class="cold-md-6" style="text-align: right">
             <div class="form-group" style="margin-bottom:unset">
               <div class="input-group">
                 <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="pt.filterGlobal($event.target.value, 'contains'); pt.resetScrollTop()" >
               </div>
             </div>
           </div>
         </div>
       </ng-template>     
       <ng-template pTemplate="header">
             <tr>
                 <th pSortableColumn="contract_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                     Contract<p-sortIcon field="contract_name"></p-sortIcon>
                 </div></th>              
                 <th pSortableColumn="period" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                   Period<p-sortIcon field="period"></p-sortIcon>
               </div></th>      
               <th    *ngIf="item.widgetDetails.Type == 'violatedMetrics'" pSortableColumn="metrics" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                Metric<p-sortIcon field="metrics"></p-sortIcon>
              </div></th>
             </tr>
             <tr *ngIf="item.showColFilters">
                 <th style="padding:unset;background-color: white !important">
                 <input style="padding: unset;font-size: 12px;margin: 0px;text-align: center;" type="search" required  id="contract_name" class="form-control" (input)="pt.filter($event.target.value, ['contract_name'] , 'contains')">     
                 </th >                
                 <th style="padding:unset;background-color: white !important">
                   <input style="padding: unset;font-size: 12px;margin: 0px;text-align: center;" type="search" required  id="period" class="form-control" (input)="pt.filter($event.target.value, ['period'] , 'contains')">
                   </th>
                   <th style="padding:unset;background-color: white !important" *ngIf="item.widgetDetails.Type == 'violatedMetrics'">
                    <input style="padding: unset;font-size: 12px;margin: 0px;text-align: center;" type="search" required  id="metrics" class="form-control" (input)="pt.filter($event.target.value, ['metrics'] , 'contains')">     
                </th>
             </tr>
         </ng-template>
         <ng-template pTemplate="groupheader" let-customer let-rowIndex="rowIndex" let-expanded="expanded">
           <tr style="  background-color: lightgray !important;">
             <td colspan="7" style="vertical-align: middle;" >
     
       <button style="width: auto; height: 0.5rem" type="button" pButton pRipple [pRowToggler]="customer" class="p-button-text p-button-rounded p-button-plain mr-2" [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                   <span  class="truncateshowField" [tooltip]= "customer.contract_name"><b>{{customer.contract_name}}</b>  </span>
                 </td>
             </tr>
         </ng-template>
         <ng-template pTemplate="rowexpansion" let-customer>
             <tr><td></td>                
                 <td style="text-align: center;" [tooltip]= "customer.period"> {{customer.period }}</td>       
                 <td *ngIf="item.widgetDetails.Type == 'violatedMetrics'"  style="text-align: center;" [tooltip]= "customer.metrics"> {{customer.metrics }}</td>      
             </tr>
         </ng-template>
     </p-table>
   </tab>
       <tab #tab1 *ngIf="item.widgetDetails.Type == 'numberExpiringContract'" heading="By contract party">  

       <p-table #pt  class="pt custom-scroll-table"  [columns]="item.colsTableExipiring2" [value]="item.tableDataExipring"  [scrollable]="true" scrollHeight="flex"  sortMode="multiple" dataKey="customer_name"  [(expandedRowKeys)]="item.expandedRowKeys" rowGroupMode="subheader" groupRowsBy="customer_name" [globalFilterFields]="['customer_name','contract_names','period']">
        <ng-template pTemplate="caption">
        <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">          
            <div>    
              <button class="btn btn-outline-dark ml-2" (click)="toggleAllRows(item.tableDataExipring, item, item.expandedRowKeys)">            
                <i [class.pi-chevron-down]="item.areAllRowsExpanded" [class.pi-chevron-right]="!item.areAllRowsExpanded" class="pi" style="vertical-align: bottom;"></i>
                {{ item.areAllRowsExpanded ? 'Collapse All' : 'Expand All' }}
            </button>    
              <button class="btn btn-outline-dark ml-2" (click)="item.showColFilters = !item.showColFilters"><i *ngIf="item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="!item.showColFilters" class=" pi pi-filter"></i></button>           
            <!--  Contracts: {{item.countRecordWidget2}} -->
              Contracts:{{pt.filteredValue?pt.filteredValue.length:pt.totalRecords}}
            </div>
            <div class="cold-md-6" style="text-align: right">
              <div class="form-group" style="margin-bottom:unset">
                <div class="input-group">
                  <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="pt.filterGlobal($event.target.value, 'contains'); pt.resetScrollTop()" >
                </div>
              </div>
            </div>
          </div>
        </ng-template>     
        <ng-template pTemplate="header">
              <tr>
                  <th pSortableColumn="customer_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                      Contract Party<p-sortIcon field="customer_name"></p-sortIcon>
                  </div></th>
                  <th pSortableColumn="contract_names" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                    Contract<p-sortIcon field="contract_names"></p-sortIcon>
                  </div></th>
                  <th pSortableColumn="period" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                    Period<p-sortIcon field="period"></p-sortIcon>
                </div></th>      
              </tr>
              <tr *ngIf="item.showColFilters">
                  <th style="padding:unset;background-color: white !important">
                  <input style="padding: unset;font-size: 12px;margin: 0px;text-align: center;" type="search" required  id="customer_name" class="form-control" (input)="pt.filter($event.target.value, ['customer_name'] , 'contains')">     
                  </th >
                  <th style="padding:unset;background-color: white !important">
                      <input style="padding: unset;font-size: 12px;margin: 0px;text-align: center;" type="search" required  id="contract_names" class="form-control" (input)="pt.filter($event.target.value, ['contract_names'] , 'contains')">     
                  </th>
                  <th style="padding:unset;background-color: white !important">
                    <input style="padding: unset;font-size: 12px;margin: 0px;text-align: center;" type="search" required  id="period" class="form-control" (input)="pt.filter($event.target.value, ['period'] , 'contains')">
                    </th>
              </tr>
          </ng-template>
          <ng-template pTemplate="groupheader" let-customer let-rowIndex="rowIndex" let-expanded="expanded">
            <tr style="  background-color: lightgray !important;">
              <td colspan="7" style="vertical-align: middle;" >
      
        <button style="width: auto; height: 0.5rem" type="button" pButton pRipple [pRowToggler]="customer" class="p-button-text p-button-rounded p-button-plain mr-2" [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                    <span  class="truncateshowField" [tooltip]= "customer.customer_name"><b>{{customer.customer_name}}</b>  </span>
                  </td>
              </tr>
          </ng-template>
          <ng-template pTemplate="rowexpansion" let-customer>
              <tr><td></td>                
                  <td style="text-align: center;" [tooltip]= "customer.contract_names">{{customer.contract_names}}</td>
                  <td style="text-align: center;" [tooltip]= "customer.period"> {{customer.period }}</td>           
              </tr>
          </ng-template>
      </p-table>
    </tab>
    <tab #tab2 heading="{{item.widgetDetails.Type === 'numberExpiringContract' ? 'By number of contracts' : item.widgetDetails.Type === 'financialPenalties' ? 'By Penalties': item.widgetDetails.Type === 'violatedMetrics' ? 'By violated metrics'  : item.widgetDetails.Type === 'violatedPeriods' ? 'By violated periods' : item.widgetDetails.Type === 'financialPenalties' ? 'By penalty' : 'By month'}}">            
  <p-table [columns]="item.colsTableExipiring"  #pt class="pt custom-scroll-table"  [value]="item.tableData" scrollHeight="flex" 
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters1 = ! item.showColFilters1"><i *ngIf=" item.showColFilters1" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters1" class=" pi pi-filter"></i></button>
        Count:{{pt.filteredValue?pt.filteredValue.length:pt.totalRecords}}
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="seriesLabel">{{ item.widgetDetails.Type === 'numberExpiringContract' ? 'Period' : (item.widgetDetails.Type === 'durationContract' || item.widgetDetails.Type === 'violatedMetrics') ? 'Contract' : 'Contract' }}<p-sortIcon field="seriesLabel"></p-sortIcon></th>  
    <th pSortableColumn="seriesCount">{{ item.widgetDetails.Type === 'numberExpiringContract' ? 'Number of Contracts' : item.widgetDetails.Type === 'violatedMetrics' ? 'violated metrics' : item.widgetDetails.Type === 'violatedPeriods' ? 'violated periods': item.widgetDetails.Type === 'financialPenalties' ? 'Penalties'  : 'Total Contracts' }} <p-sortIcon field="seriesCount"></p-sortIcon></th>                
 
  </tr>
  <tr *ngIf=" item.showColFilters1" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important"  [hidden]= "col.field == 'seriesCompliant'">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style="text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriesLabel"  data-container="body">{{row.seriesLabel}}</td>  
        <td *ngIf="item.widgetDetails.Type !== 'financialPenalties'" style="text-align:center"  class="truncateModal" [tooltip]= "row.seriesCount"  data-container="body">{{row.seriesCount}}</td>  
        <td *ngIf="item.widgetDetails.Type === 'financialPenalties'" style="text-align:center" class="truncateModal" [tooltip]="row.seriesCount + ' ' + row.seriesSymbol" data-container="body">{{ row.seriesCount }} {{ row.seriesSymbol }}</td>
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
    </tab>
 
</tabset>

  </div>
  
<!--  END EXPIRATION-->
<!--  Trend Customer-->

<div *ngIf="item.widgetDetails.Type == 'calculationCustomerAll' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationCustomerAll' && item.widgetData.chartRecord != 'chart'">
  <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
  </div>

        <div *ngIf="item.widgetDetails.Type == 'calculationCustomerMonth' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationCustomerMonth' && item.widgetData.chartRecord != 'chart'">
        <div  style=" min-height: 450px; min-width: 1px;" echarts [options]="item.data" class="mt-4"  (chartClick)="onChartClickCP($event, item)"></div>                     
        </div>
        <div *ngIf="item.widgetDetails.Type == 'calculationCustomerWeek' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationCustomerWeek' && item.widgetData.chartRecord != 'chart'">
          <div  style=" min-height: 450px; min-width: 1px;" echarts [options]="item.data" class="mt-4"  (chartClick)="onChartClickCP($event, item)"></div>                     
          </div>

        <div *ngIf="item.widgetDetails.Type == 'calculationCustomerDays' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationCustomerDays' && item.widgetData.chartRecord != 'chart'">
        <div  style=" min-height: 450px; min-width: 1px;" echarts [options]="item.data" class="mt-4" (chartClick)="onChartClickCP($event, item)"></div>                     
        </div>

        
        <div *ngIf="item.widgetDetails.Type == 'calculationCustomerSemester' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationCustomerSemester' && item.widgetData.chartRecord != 'chart'">
        <div  style=" min-height: 450px; min-width: 1px;" echarts [options]="item.data" class="mt-4" (chartClick)="onChartClickCP($event, item)"></div>                     
        </div>

          
        <div *ngIf="item.widgetDetails.Type == 'calculationCustomerQuarter' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationCustomerQuarter' && item.widgetData.chartRecord != 'chart'">
        <div  style=" min-height: 450px; min-width: 1px;" echarts [options]="item.data" class="mt-4"  (chartClick)="onChartClickCP($event, item)"></div>                     
        </div>

          
        <div *ngIf="item.widgetDetails.Type == 'calculationCustomerYear' && item.widgetData.chartRecord == 'chart'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationCustomerYear' && item.widgetData.chartRecord != 'chart'">
        <div  style=" min-height: 450px; min-width: 1px;" echarts [options]="item.data" class="mt-4"  (chartClick)="onChartClickCP($event, item)"></div>                     
        </div>
<!-- End  Trend-->










<!--  PENALTY    -->
<div *ngIf="item.widgetDetails.Type == 'calculationMonth' && item.widgetData.chartRecord == 'chart'   && item.widgetData.calcPenalty == 'penalty'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationMonth' && item.widgetData.chartRecord != 'chart' && item.widgetData.calcPenalty != 'penalty'">
  <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
</div>
<div *ngIf="item.widgetDetails.Type == 'calculationWeek' && item.widgetData.chartRecord == 'chart'   && item.widgetData.calcPenalty == 'penalty'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationWeek' && item.widgetData.chartRecord != 'chart' && item.widgetData.calcPenalty != 'penalty'">
  <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
</div>
<div *ngIf="item.widgetDetails.Type == 'calculationDays' && item.widgetData.chartRecord == 'chart'   && item.widgetData.calcPenalty == 'penalty'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationDays' && item.widgetData.chartRecord != 'chart' && item.widgetData.calcPenalty != 'penalty'">
  <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
</div>
<div *ngIf="item.widgetDetails.Type == 'calculationYear' && item.widgetData.chartRecord == 'chart'   && item.widgetData.calcPenalty == 'penalty'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationYear' && item.widgetData.chartRecord != 'chart' && item.widgetData.calcPenalty != 'penalty'">
  <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
</div>
<div *ngIf="item.widgetDetails.Type == 'calculationQuarter' && item.widgetData.chartRecord == 'chart'   && item.widgetData.calcPenalty == 'penalty'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationQuarter' && item.widgetData.chartRecord != 'chart' && item.widgetData.calcPenalty != 'penalty'">
  <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
</div>

<div *ngIf="item.widgetDetails.Type == 'calculationSemester' && item.widgetData.chartRecord == 'chart'   && item.widgetData.calcPenalty == 'penalty'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationSemester' && item.widgetData.chartRecord != 'chart' && item.widgetData.calcPenalty != 'penalty'">
  <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
</div>

<div *ngIf="item.widgetDetails.Type == 'calculationWeek' && item.widgetData.chartRecord == 'record'  && item.widgetData.calcPenalty == 'penalty'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationWeek' && item.widgetData.chartRecord != 'record'  && item.widgetData.calcPenalty != 'penalty'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
    <th pSortableColumn="seriePenalty">penalty<p-sortIcon field="seriePenalty"></p-sortIcon></th>      
    <th pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th> 
    <th>actions</th> 
 
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
        <td style="text-align:center"  [tooltip]= "row.seriePenalty + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.seriePenalty}} {{row.unitMeasureSymbol}}</td>  
        <td style="text-align:center">
          <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
          <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
        </td>
        <td style="text-align:center">
          <button *ngIf="row.partial_period !== ''" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                   <i class="fa-solid fa-folder-open"></i>
                 </button>
                 <button *ngIf="row.partial_period === ''" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                   <i class="fa-solid fa-folder-open"></i>
                 </button>
               </td>
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
  </div>
<div *ngIf="item.widgetDetails.Type == 'calculationMonth' && item.widgetData.chartRecord == 'record'  && item.widgetData.calcPenalty == 'penalty'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationMonth' && item.widgetData.chartRecord != 'record'  && item.widgetData.calcPenalty != 'penalty'">
             
  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
  <ng-template pTemplate="caption">
    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
      <div>
        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
        Count: {{item.countRecordWidget}} 
      </div>
        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
          <div class="input-group">
            <div class="input-group-prepend">
            <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
            </div>
            <div  class="gridster-item-content">
            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
        </div>  </div>
        </div>
    </div>
  </ng-template>
  <ng-template pTemplate="header" let-row let-columns >
  <tr class="fuseqTableHeader" style="white-space: nowrap;">
    <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
    <th pSortableColumn="seriePenalty">penalty<p-sortIcon field="seriePenalty"></p-sortIcon></th>   
    <th pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>                    
    <th>actions</th>   
  </tr>
  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
   <th  *ngFor="let col of columns" style="background-color: white !important">
    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
     style=" text-align:center"  colpsan= "14"
      placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
    </th>
  </tr>
</ng-template>
    <ng-template pTemplate="body" let-row  let-i="index">
      <tr >
        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
        <td style="text-align:center" [tooltip]="row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : ''"  data-container="body">{{ row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : '' }}</td>
        <td *ngIf="row.seriePenalty !== ''" style="text-align:center">
             <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
             <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>         
           </td>
           <td  *ngIf="row.seriePenalty == ''"  style="text-align:center">{{ '' }}</td>
        <td style="text-align:center">
          <button *ngIf="row.partial_period !== ''" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                   <i class="fa-solid fa-folder-open"></i>
                 </button>
                 <button *ngIf="row.partial_period === ''" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                   <i class="fa-solid fa-folder-open"></i>
                 </button>
               </td>
      </tr>
    </ng-template>
<!--        <ng-template pTemplate="emptymessage">
        <td style="text-align:center" colspan="10">No data</td>
  </ng-template>--> 

 </p-table>
  </div>
  <div *ngIf="item.widgetDetails.Type == 'calculationDays' && item.widgetData.chartRecord == 'record'  && item.widgetData.calcPenalty == 'penalty'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationDays' && item.widgetData.chartRecord != 'record'  && item.widgetData.calcPenalty != 'penalty'">
             
    <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
    [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
    <ng-template pTemplate="caption">
      <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
        <div>
          <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
          Count: {{item.countRecordWidget}} 
        </div>
          <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
            <div class="input-group">
              <div class="input-group-prepend">
              <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
              </div>
              <div  class="gridster-item-content">
              <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
          </div>  </div>
          </div>
      </div>
    </ng-template>
    <ng-template pTemplate="header" let-row let-columns >
    <tr class="fuseqTableHeader" style="white-space: nowrap;">
      <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
      <th pSortableColumn="seriePenalty">penalty<p-sortIcon field="seriePenalty"></p-sortIcon></th>   
      <th pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>                    
      <th>actions</th>   
    </tr>
    <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
     <th  *ngFor="let col of columns" style="background-color: white !important">
      <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
       style=" text-align:center"  colpsan= "14"
        placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
  
        [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
        type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
      </th>
    </tr>
  </ng-template>
      <ng-template pTemplate="body" let-row  let-i="index">
        <tr >
          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
          <td style="text-align:center" [tooltip]="row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : ''"  data-container="body">{{ row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : '' }}</td>
       <td *ngIf="row.seriePenalty !== ''" style="text-align:center">
            <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
            <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>         
          </td>
          <td  *ngIf="row.seriePenalty == ''"  style="text-align:center">{{ '' }}</td>
          <td style="text-align:center">
            <button *ngIf="row.partial_period !== ''" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                     <i class="fa-solid fa-folder-open"></i>
                   </button>
                   <button *ngIf="row.partial_period === ''" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                     <i class="fa-solid fa-folder-open"></i>
                   </button>
                 </td>
        </tr>
      </ng-template>
  <!--        <ng-template pTemplate="emptymessage">
          <td style="text-align:center" colspan="10">No data</td>
    </ng-template>--> 
  
   </p-table>
    </div>

    <div *ngIf="item.widgetDetails.Type == 'calculationQuarter' && item.widgetData.chartRecord == 'record'  && item.widgetData.calcPenalty == 'penalty'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationQuarter' && item.widgetData.chartRecord != 'record'  && item.widgetData.calcPenalty != 'penalty'">
             
      <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
      [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
      <ng-template pTemplate="caption">
        <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
          <div>
            <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
            Count: {{item.countRecordWidget}} 
          </div>
            <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
              <div class="input-group">
                <div class="input-group-prepend">
                <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                </div>
                <div  class="gridster-item-content">
                <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
            </div>  </div>
            </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header" let-row let-columns >
      <tr class="fuseqTableHeader" style="white-space: nowrap;">
        <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
        <th pSortableColumn="seriePenalty">penalty<p-sortIcon field="seriePenalty"></p-sortIcon></th>        
        <th pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>               
        <th>actions</th>   
      </tr>
      <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
       <th  *ngFor="let col of columns" style="background-color: white !important">
        <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
         style=" text-align:center"  colpsan= "14"
          placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
    
          [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
          type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
        </th>
      </tr>
    </ng-template>
        <ng-template pTemplate="body" let-row  let-i="index">
          <tr >
            <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
            <td style="text-align:center" [tooltip]="row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : ''"  data-container="body">{{ row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : '' }}</td>
            <td *ngIf="row.seriePenalty !== ''" style="text-align:center">
                 <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                 <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>         
               </td>
               <td  *ngIf="row.seriePenalty == ''"  style="text-align:center">{{ '' }}</td>
            <td style="text-align:center">
              <button *ngIf="row.partial_period !== ''" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                       <i class="fa-solid fa-folder-open"></i>
                     </button>
                     <button *ngIf="row.partial_period === ''" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                       <i class="fa-solid fa-folder-open"></i>
                     </button>
                   </td>
          </tr>
        </ng-template>
    <!--        <ng-template pTemplate="emptymessage">
            <td style="text-align:center" colspan="10">No data</td>
      </ng-template>--> 
    
     </p-table>
      </div>
      <div *ngIf="item.widgetDetails.Type == 'calculationSemester' && item.widgetData.chartRecord == 'record'  && item.widgetData.calcPenalty == 'penalty'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationSemester' && item.widgetData.chartRecord != 'record'  && item.widgetData.calcPenalty != 'penalty'">
             
        <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
        [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
        <ng-template pTemplate="caption">
          <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
            <div>
              <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
              Count: {{item.countRecordWidget}} 
            </div>
              <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                <div class="input-group">
                  <div class="input-group-prepend">
                  <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                  </div>
                  <div  class="gridster-item-content">
                  <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
              </div>  </div>
              </div>
          </div>
        </ng-template>
        <ng-template pTemplate="header" let-row let-columns >
        <tr class="fuseqTableHeader" style="white-space: nowrap;">
          <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
          <th pSortableColumn="seriePenalty">penalty<p-sortIcon field="seriePenalty"></p-sortIcon></th>  
          <th pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>                     
          <th>actions</th>   
        </tr>
        <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
         <th  *ngFor="let col of columns" style="background-color: white !important">
          <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
           style=" text-align:center"  colpsan= "14"
            placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
      
            [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
            type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
          </th>
        </tr>
      </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
              <td style="text-align:center"  [tooltip]= "row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : ''"  data-container="body">{{ row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : '' }}</td>
       <td *ngIf="row.seriePenalty !== ''" style="text-align:center">
            <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
            <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>         
          </td>
          <td  *ngIf="row.seriePenalty == ''"  style="text-align:center">{{ '' }}</td>
              <td style="text-align:center">
                <button *ngIf="row.partial_period !== ''" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                         <i class="fa-solid fa-folder-open"></i>
                       </button>
                       <button *ngIf="row.partial_period === ''" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                         <i class="fa-solid fa-folder-open"></i>
                       </button>
                     </td>
            </tr>
          </ng-template>
      <!--        <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="10">No data</td>
        </ng-template>--> 
      
       </p-table>
        </div>
        <div *ngIf="item.widgetDetails.Type == 'calculationYear' && item.widgetData.chartRecord == 'record'  && item.widgetData.calcPenalty == 'penalty'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationYear' && item.widgetData.chartRecord != 'record'  && item.widgetData.calcPenalty != 'penalty'">
             
          <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
          [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm">
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
                Count: {{item.countRecordWidget}} 
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                    <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                    </div>
                    <div  class="gridster-item-content">
                    <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
                </div>  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-row let-columns >
          <tr class="fuseqTableHeader" style="white-space: nowrap;">
            <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
            <th pSortableColumn="seriePenalty">penalty<p-sortIcon field="seriePenalty"></p-sortIcon></th>  
            <th pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>                       
            <th>actions</th>   
          </tr>
          <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
           <th  *ngFor="let col of columns" style="background-color: white !important">
            <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
             style=" text-align:center"  colpsan= "14"
              placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
        
              [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
              type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
            </th>
          </tr>
        </ng-template>
            <ng-template pTemplate="body" let-row  let-i="index">
              <tr >
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
                <td style="text-align:center" [tooltip]="row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : ''"  data-container="body">{{ row.seriePenalty && row.seriePenalty.trim() ? row.seriePenalty + ' ' + row.unitMeasureSymbol : '' }}</td>
                <td *ngIf="row.seriePenalty !== ''" style="text-align:center">
                     <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                     <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>         
                   </td>
                   <td  *ngIf="row.seriePenalty == ''"  style="text-align:center">{{ '' }}</td>
                <td style="text-align:center">
                  <button *ngIf="row.partial_period !== ''" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                           <i class="fa-solid fa-folder-open"></i>
                         </button>
                         <button *ngIf="row.partial_period === ''" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                           <i class="fa-solid fa-folder-open"></i>
                         </button>
                       </td>
              </tr>
            </ng-template>
        <!--        <ng-template pTemplate="emptymessage">
                <td style="text-align:center" colspan="10">No data</td>
          </ng-template>--> 
        
         </p-table>
          </div>

<!--   CALCULATION    -->
<div *ngIf="item.widgetDetails.Type == 'calculationWeek' && item.widgetData.chartRecord == 'chart' && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationWeek' && item.widgetData.chartRecord != 'chart' &&  item.widgetData.calcPenalty != 'calculation'">
             
  <!-- <div class="mt-2 mb-1" style="padding-left: 9px !important">
      <button class="btn btn-sm btn-outline-primary" data-toggle="modal"
      container="body" tooltip="Record" (click)="showRecord('month')" style="font-size:10px; position: absolute;
     ">
      Table
    </button>
    </div>-->                             
   <!--   <div style=" min-height: 350px; min-width: 1px;"  echarts 
     [options]="optionsTrendM" (chartInit)="onChartInit('month', $event,'chartTrend')" 
     class="mt-4"></div>   -->
         <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
    </div>
    <div *ngIf="item.widgetDetails.Type == 'calculationWeek' && item.widgetData.chartRecord == 'record'  && item.widgetData.calcPenalty == 'calculation'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationWeek' && item.widgetData.chartRecord != 'record'  && item.widgetData.calcPenalty != 'calculation'">
             
      <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
      [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" (sortFunction)="customSort($event)" [customSort]="true">
      <ng-template pTemplate="caption">
        <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
          <div>
            <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
            Count: {{item.countRecordWidget}} 
          </div>
            <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
              <div class="input-group">
                <div class="input-group-prepend">
                <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                </div>
                <div  class="gridster-item-content">
                <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
            </div>  </div>
            </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header" let-row let-columns >
        <tr class="fuseqTableHeader" style="white-space: nowrap;">
          <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
          <th  *ngIf="item.hiddenTarget  == true"  pSortableColumn="target">target<p-sortIcon field="target"></p-sortIcon></th> 
          <th pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th>   
          <th *ngIf="item.hiddenTarget  == true" pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th>                     
          <th  pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>  
          <th>actions</th>
        </tr>
        <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
         <th  *ngFor="let col of columns" style="background-color: white !important" [hidden]="(item.hiddenTarget == false && (col.field == 'target' || col.field == 'compliant'))"
         > 
          <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
           style=" text-align:center"  colpsan= "14"
           placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
    
            [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
            type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
          </th>
        </tr>
      </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
              <td *ngIf="item.hiddenTarget  == true" style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.result" data-container="body">{{row.result}}</td>   
              <td *ngIf="item.hiddenTarget  == true" style="text-align:center">
                <i *ngIf="row.compliant == true" class="fa fa-check fq-success-color"></i>
                <i *ngIf="row.compliant == false" class="fa fa-times fq-danger-color"></i>
              </td>
              <td style="text-align:center">
                <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
              </td>
              <td style="text-align:center">
                <button *ngIf="row.partial_period !== null" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                  <i class="fa-solid fa-folder-open"></i>
                </button>
                <button *ngIf="row.partial_period === null" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                  <i class="fa-solid fa-folder-open"></i>
                </button>
              </td>
            </tr>
          </ng-template>
<!--        <ng-template pTemplate="emptymessage">
            <td style="text-align:center" colspan="10">No data</td>
      </ng-template>--> 

     </p-table>
      </div>

<div *ngIf="item.widgetDetails.Type == 'calculationDays' && item.widgetData.chartRecord == 'chart' && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationDays'  && item.widgetData.chartRecord != 'chart' && item.widgetData.calcPenalty != 'calculation'" >
             
  <!--  <div class="mt-2 mb-1" style="padding-left: 9px !important">
      <button class="btn btn-sm btn-outline-primary" data-toggle="modal"
      container="body" tooltip="Record" (click)="showRecord('day')" style="font-size:10px; position: absolute;
     ">
      Table
    </button>
    </div>-->
  <!-- <div style=" min-height: 350px; min-width: 1px;" id="{{item.id}}" echarts  [options]="optionsTrendD" (chartInit)="onChartInit('day', $event,'chartTrendDay')" class="mt-4"></div>-->       
    <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>      
  </div>
    <div *ngIf="item.widgetDetails.Type == 'calculationDays' && item.widgetData.chartRecord == 'record' && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationDays' && item.widgetData.chartRecord != 'record' && item.widgetData.calcPenalty != 'calculation'">
     
      <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
      [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" (sortFunction)="customSort($event)" [customSort]="true">
      <ng-template pTemplate="caption">
        <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
          <div>
            <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
            Count: {{item.countRecordWidget}}
          </div>
            <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
              <div class="input-group">
                <div class="input-group-prepend">
                <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                </div>
                <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search" InputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
              </div>
            </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header" let-row let-columns >
        <tr class="fuseqTableHeader" style="white-space: nowrap;">
          <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
          <th  *ngIf="item.hiddenTarget  == true"  pSortableColumn="target">target<p-sortIcon field="target"></p-sortIcon></th> 
          <th pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th>   
          <th *ngIf="item.hiddenTarget  == true" pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th>                     
          <th  pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>  
          <th>actions</th>
        </tr>
        <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
         <th  *ngFor="let col of columns" style="background-color: white !important" [hidden]="(item.hiddenTarget == false && (col.field == 'target' || col.field == 'compliant'))"
         > 
          <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
           style=" text-align:center"  colpsan= "14"
           placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
    
            [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
            type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
          </th>
        </tr>
      </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
              <td *ngIf="item.hiddenTarget  == true" style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.result" data-container="body">{{row.result}}</td>   
              <td *ngIf="item.hiddenTarget  == true" style="text-align:center">
                <i *ngIf="row.compliant == true" class="fa fa-check fq-success-color"></i>
                <i *ngIf="row.compliant == false" class="fa fa-times fq-danger-color"></i>
              </td>
              <td style="text-align:center">
                <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
              </td>
              <td style="text-align:center">
                <button *ngIf="row.partial_period !== null" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                  <i class="fa-solid fa-folder-open"></i>
                </button>
                <button *ngIf="row.partial_period === null" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                  <i class="fa-solid fa-folder-open"></i>
                </button>
              </td>
            </tr>
          </ng-template>
      <!--    <ng-template pTemplate="emptymessage">
            <td style="text-align:center" colspan="10">No data</td>
      </ng-template> -->

     </p-table>
      </div>

            <div *ngIf="item.widgetDetails.Type == 'calculationMonth' && item.widgetData.chartRecord == 'chart' && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationMonth' && item.widgetData.chartRecord != 'chart' &&  item.widgetData.calcPenalty != 'calculation'">
             
            <!-- <div class="mt-2 mb-1" style="padding-left: 9px !important">
                <button class="btn btn-sm btn-outline-primary" data-toggle="modal"
                container="body" tooltip="Record" (click)="showRecord('month')" style="font-size:10px; position: absolute;
               ">
                Table
              </button>
              </div>-->                             
             <!--   <div style=" min-height: 350px; min-width: 1px;"  echarts 
               [options]="optionsTrendM" (chartInit)="onChartInit('month', $event,'chartTrend')" 
               class="mt-4"></div>   -->
                   <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>                     
              </div>
              <div *ngIf="item.widgetDetails.Type == 'calculationMonth' && item.widgetData.chartRecord == 'record'  && item.widgetData.calcPenalty == 'calculation'"  class="clickable col-md-6"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationMonth' && item.widgetData.chartRecord != 'record'  && item.widgetData.calcPenalty != 'calculation'">
             
                <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
                [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" (sortFunction)="customSort($event)" [customSort]="true">
                <ng-template pTemplate="caption">
                  <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
                    <div>
                      <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
                      Count: {{item.countRecordWidget}} 
                    </div>
                      <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                        <div class="input-group">
                          <div class="input-group-prepend">
                          <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                          </div>
                          <div  class="gridster-item-content">
                          <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
                      </div>  </div>
                      </div>
                  </div>
                </ng-template>
                <ng-template pTemplate="header" let-row let-columns >
                  <tr class="fuseqTableHeader" style="white-space: nowrap;">
                    <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
                    <th  *ngIf="item.hiddenTarget  == true"  pSortableColumn="target">target<p-sortIcon field="target"></p-sortIcon></th> 
                    <th pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th>   
                    <th *ngIf="item.hiddenTarget  == true" pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th>                     
                    <th  pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>  
                    <th>actions</th>
                  </tr>
                  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
                   <th  *ngFor="let col of columns" style="background-color: white !important" [hidden]="(item.hiddenTarget == false && (col.field == 'target' || col.field == 'compliant'))"
                   > 
                    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
                     style=" text-align:center"  colpsan= "14"
                     placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
    
                      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
                      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
                    </th>
                  </tr>
                </ng-template>
                    <ng-template pTemplate="body" let-row  let-i="index">
                      <tr >
                        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
                        <td *ngIf="item.hiddenTarget  == true" style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>
                        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.result" data-container="body">{{row.result}}</td>   
                        <td *ngIf="item.hiddenTarget  == true" style="text-align:center">
                          <i *ngIf="row.compliant == true" class="fa fa-check fq-success-color"></i>
                          <i *ngIf="row.compliant == false" class="fa fa-times fq-danger-color"></i>
                        </td>
                        <td style="text-align:center">
                          <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                          <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
                        </td>
                        <td style="text-align:center">
                          <button *ngIf="row.partial_period !== null" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                            <i class="fa-solid fa-folder-open"></i>
                          </button>
                          <button *ngIf="row.partial_period === null" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                            <i class="fa-solid fa-folder-open"></i>
                          </button>
                        </td>
                      </tr>
                    </ng-template>
         <!--        <ng-template pTemplate="emptymessage">
                      <td style="text-align:center" colspan="10">No data</td>
                </ng-template>--> 
  
               </p-table>
                </div>




            <div *ngIf="item.widgetDetails.Type == 'calculationYear'  && item.widgetData.chartRecord == 'chart'  && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationYear'  && item.widgetData.chartRecord != 'chart'  && item.widgetData.calcPenalty != 'calculation'">
             
             <!-- <div class="mt-2 mb-1" style="padding-left: 9px !important">
                <button class="btn btn-sm btn-outline-primary" data-toggle="modal"
                container="body" tooltip="Record" (click)="showRecord('year')" style="font-size:10px; position: absolute;
                ">
                Table
              </button>
              </div>-->
             <!-- <div style=" min-height: 350px; min-width: 1px;" echarts  [options]="optionsTrendY" (chartInit)="onChartInit('year', $event,'chartTrendYear')" class="mt-4"></div> -->     
            
              <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>     
            
            </div>
              <div *ngIf="item.widgetDetails.Type == 'calculationYear' && item.widgetData.chartRecord == 'record' && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationYear' && item.widgetData.chartRecord != 'record' && item.widgetData.calcPenalty != 'calculation'">
             
                <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
                [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" (sortFunction)="customSort($event)" [customSort]="true">
                <ng-template pTemplate="caption">
                  <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
                    <div>
                      <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
                      Count: {{item.countRecordWidget}}
                    </div>
                      <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                        <div class="input-group">
                          <div class="input-group-prepend">
                          <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                          </div>
                          <div  class="gridster-item-content">
                          <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search" pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
                        </div> </div>
                      </div>
                  </div>
                </ng-template>
                <ng-template pTemplate="header" let-row let-columns >
                  <tr class="fuseqTableHeader" style="white-space: nowrap;">
                    <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
                    <th  *ngIf="item.hiddenTarget  == true"  pSortableColumn="target">target<p-sortIcon field="target"></p-sortIcon></th> 
                    <th pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th>   
                    <th *ngIf="item.hiddenTarget  == true" pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th>                     
                    <th  pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th> 
                    <th>actions</th> 
                  </tr>
                  <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
                   <th  *ngFor="let col of columns" style="background-color: white !important" [hidden]="(item.hiddenTarget == false && (col.field == 'target' || col.field == 'compliant'))"
                   > 
                    <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
                     style=" text-align:center"  colpsan= "14"
                     placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
    
                      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
                      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
                    </th>
                  </tr>
                </ng-template>
                    <ng-template pTemplate="body" let-row  let-i="index">
                      <tr >
                        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
                        <td *ngIf="item.hiddenTarget  == true" style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>
                        <td style="text-align:center"  class="truncateModal" [tooltip]= "row.result" data-container="body">{{row.result}}</td>   
                        <td *ngIf="item.hiddenTarget  == true" style="text-align:center">
                          <i *ngIf="row.compliant == true" class="fa fa-check fq-success-color"></i>
                          <i *ngIf="row.compliant == false" class="fa fa-times fq-danger-color"></i>
                        </td>
                        <td style="text-align:center">
                          <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                          <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
                        </td>
                        <td style="text-align:center">
                          <button *ngIf="row.partial_period !== null" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                            <i class="fa-solid fa-folder-open"></i>
                          </button>
                          <button *ngIf="row.partial_period === null" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                            <i class="fa-solid fa-folder-open"></i>
                          </button>
                        </td>
                      </tr>
                    </ng-template>
               <!--     <ng-template pTemplate="emptymessage">
                      <td style="text-align:center" colspan="10">No data</td>
                </ng-template>-->
  
               </p-table>
                </div>
  
              <div *ngIf="item.widgetDetails.Type == 'calculationSemester'  && item.widgetData.chartRecord == 'chart' && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationSemester'  && item.widgetData.chartRecord != 'chart' && item.widgetData.calcPenalty != 'calculation'">
             
             <!--   <div class="mt-2 mb-1" style="padding-left: 9px !important">
                  <button class="btn btn-sm btn-outline-primary" data-toggle="modal"
                  container="body" tooltip="Record" (click)="showRecord('semester')" style="font-size:10px; position: absolute;
                 ">
                 Table
                </button>
                </div>-->
            <!--   <div style=" min-height: 350px; min-width: 1px;" id="{{item.id}}" echarts  [options]="optionsTrendS" (chartInit)="onChartInit('semester', $event,'chartTrendSemester')" class="mt-4"></div> -->      
                <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>    
              </div>
                <div *ngIf="item.widgetDetails.Type == 'calculationSemester' && item.widgetData.chartRecord == 'record' && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationSemester' && item.widgetData.chartRecord != 'record' && item.widgetData.calcPenalty != 'calculation'">
             
                  <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
                  [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" (sortFunction)="customSort($event)" [customSort]="true">
                  <ng-template pTemplate="caption">
                    <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
                      <div>
                        <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
                        Count: {{item.countRecordWidget}}
                      </div>
                        <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                          <div class="input-group">
                            <div class="input-group-prepend">
                    <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                            </div>
                            <div  class="gridster-item-content">
                            <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search" pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()">
                          </div>  </div>
                        </div>
                    </div>
                  </ng-template>
                  <ng-template pTemplate="header" let-row let-columns >
                    <tr class="fuseqTableHeader" style="white-space: nowrap;">
                      <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
                      <th  *ngIf="item.hiddenTarget  == true"  pSortableColumn="target">target<p-sortIcon field="target"></p-sortIcon></th> 
                      <th pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th>   
                      <th *ngIf="item.hiddenTarget  == true" pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th>                     
                      <th  pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>  
                      <th>actions</th>
                    </tr>
                    <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
                     <th  *ngFor="let col of columns" style="background-color: white !important" [hidden]="(item.hiddenTarget == false && (col.field == 'target' || col.field == 'compliant'))"
                     > 
                      <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
                       style=" text-align:center"  colpsan= "14"
                       placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
    
                        [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
                        type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
                      </th>
                    </tr>
                  </ng-template>
                      <ng-template pTemplate="body" let-row  let-i="index">
                        <tr >
                          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
                          <td *ngIf="item.hiddenTarget  == true" style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>
                          <td style="text-align:center"  class="truncateModal" [tooltip]= "row.result" data-container="body">{{row.result}}</td>   
                          <td *ngIf="item.hiddenTarget  == true" style="text-align:center">
                            <i *ngIf="row.compliant == true" class="fa fa-check fq-success-color"></i>
                            <i *ngIf="row.compliant == false" class="fa fa-times fq-danger-color"></i>
                          </td>
                          <td style="text-align:center">
                            <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                            <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
                          </td>
                          <td style="text-align:center">
                            <button *ngIf="row.partial_period !== null" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                              <i class="fa-solid fa-folder-open"></i>
                            </button>
                            <button *ngIf="row.partial_period === null" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                              <i class="fa-solid fa-folder-open"></i>
                            </button>
                          </td>
                        </tr>
                      </ng-template>
                  <!--    <ng-template pTemplate="emptymessage">
                        <td style="text-align:center" colspan="10">No data</td>
                  </ng-template> -->
    
                 </p-table>
                  </div>
                <div *ngIf="item.widgetDetails.Type == 'calculationQuarter'  && item.widgetData.chartRecord == 'chart' && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationQuarter'  && item.widgetData.chartRecord != 'chart' && item.widgetData.calcPenalty != 'calculation'">
             
             <!--     <div class="mt-2 mb-1" style="padding-left: 9px !important">
                    <button class="btn btn-sm btn-outline-primary" data-toggle="modal"
                    container="body" tooltip="Record" (click)="showRecord('quarter')" style="font-size:10px; position: absolute;
                  ">
                   Table
                  </button>
                  </div>-->
                <!--  <div style=" min-height: 350px; min-width: 1px;" id="{{item.id}}" echarts  [options]="optionsTrendQ" (chartInit)="onChartInit('quarter', $event,'chartTrendQuarter')" class="mt-4"></div>  -->    
                  <div  style=" min-height: 500px; min-width: 1px;" echarts [options]="item.data" class="mt-4"></div>    
                </div>
                  <div *ngIf="item.widgetDetails.Type == 'calculationQuarter' && item.widgetData.chartRecord == 'record' && item.widgetData.calcPenalty == 'calculation'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'calculationQuarter' && item.widgetData.chartRecord != 'record' && item.widgetData.calcPenalty != 'calculation'">
             
                    <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData" scrollHeight="flex" #indicatorsTable
                    [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" (sortFunction)="customSort($event)" [customSort]="true">
                    <ng-template pTemplate="caption">
                      <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
                        <div>
                          <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
                          Count: {{item.countRecordWidget}}
                        </div>
                          <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                            <div class="input-group">
                              <div class="input-group-prepend">
                               <!-- <span class="input-group-text" style="min-width: 95px;">Global Search</span>--> 
                              </div>
                              <div  class="gridster-item-content">
                              <input type="search" class="form-control" style="width: auto; max-width: 150px;" placeholder="Search"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" >
                            </div> </div>
                          </div>
                      </div>
                    </ng-template>
                    <ng-template pTemplate="header" let-row let-columns >
                      <tr class="fuseqTableHeader" style="white-space: nowrap;">
                        <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
                        <th  *ngIf="item.hiddenTarget  == true"  pSortableColumn="target">target<p-sortIcon field="target"></p-sortIcon></th> 
                        <th pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th>   
                        <th *ngIf="item.hiddenTarget  == true" pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th>                     
                        <th  pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>  
                        <th>actions</th>
                      </tr>
                      <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
                       <th  *ngFor="let col of columns" style="background-color: white !important" [hidden]="(item.hiddenTarget == false && (col.field == 'target' || col.field == 'compliant'))"
                       > 
                        <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
                         style=" text-align:center"  colpsan= "14"
                         placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
    
                          [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
                          type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()"></div>
                        </th>
                      </tr>
                    </ng-template>
                        <ng-template pTemplate="body" let-row  let-i="index">
                          <tr >
                            <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>   
                            <td *ngIf="item.hiddenTarget  == true" style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>
                            <td style="text-align:center"  class="truncateModal" [tooltip]= "row.result" data-container="body">{{row.result}}</td>   
                            <td *ngIf="item.hiddenTarget  == true" style="text-align:center">
                              <i *ngIf="row.compliant == true" class="fa fa-check fq-success-color"></i>
                              <i *ngIf="row.compliant == false" class="fa fa-times fq-danger-color"></i>
                            </td>
                            <td style="text-align:center">
                              <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                              <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
                            </td>
                            <td style="text-align:center">
                              <button *ngIf="row.partial_period !== null" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                                <i class="fa-solid fa-folder-open"></i>
                              </button>
                              <button *ngIf="row.partial_period === null" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                                <i class="fa-solid fa-folder-open"></i>
                              </button>
                            </td>
                          </tr>
                        </ng-template>
                 <!--       <ng-template pTemplate="emptymessage">
                          <td style="text-align:center" colspan="10">No data</td>
                    </ng-template> -->
      
                   </p-table>
                    </div>
            <div *ngIf="item.type == 'pie'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'pie'">
              <div  *ngIf="!item.conferror" class="chartjs-container"  >
                <canvas
                baseChart
                [data]="item.data"
                [labels]="item.label"
                [chartType]="item.type"
                [colors]=item.color
                [options]="item.option"
                >
                </canvas>
              </div>
              <div *ngIf="item.conferror"> 
                <div class="box-header with-border" style="padding-top: 20px;text-align: center;">
      <!--<p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Key licence expired</b></p>-->
      <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Unable to convert the value of the 'result' column to an integer. Ensure all values are numeric</b></p>
      </div>
            </div>
           </div>
           <div *ngIf="item.type == 'doughnut'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'doughnut'">
              <div *ngIf="!item.conferror" class="chartjs-container"  >
                <canvas
                baseChart
                [data]="item.data"
                [labels]="item.label"
                [chartType]="item.type"
                [colors]=item.color
                [options]="item.option"
                >
                </canvas>
              </div>
              <div *ngIf="item.conferror"> 
                <div class="box-header with-border" style="padding-top: 20px;text-align: center;">
      <!--<p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Key licence expired</b></p>-->
      <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Unable to convert the value of the 'result' column to an integer. Ensure all values are numeric</b></p>
      </div>
            </div>
           </div>
           <div *ngIf="item.type == 'line'" class="clickable sm-4"  style="display: contents;"  [hidden]="item.widgetDetails.Type != 'line'">
           <div *ngIf="!item.conferror" class="chartjs-container" >
            <canvas
            baseChart
            [datasets]="item.data"
            [labels]="item.label"

            [colors]="item.color"
            [chartType]="item.type"
            [options]="item.option"
            >
           </canvas>
              </div>
              <div *ngIf="item.conferror"> 
                  <div class="box-header with-border" style="padding-top: 20px;text-align: center;">
        <!--<p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Key licence expired</b></p>-->
        <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Unable to convert the value of the 'result' column to an integer. Ensure all values are numeric</b></p>
        </div>
              </div>
           </div>
           <div *ngIf="item.type == 'bar'" class="clickable col-md-6"  style="display: contents" [hidden]="item.widgetDetails.Type != 'bar'" >
              <div *ngIf="!item.conferror" class="chartjs-container">
               <canvas baseChart
                  [datasets]="item.data"
                  [labels]="item.label"
                  [chartType]="item.type"
                  [colors]="item.color"
                  [options]="item.option">
               </canvas>
              </div>
              <div *ngIf="item.conferror"> 
                <div class="box-header with-border" style="padding-top: 20px;text-align: center;">
      <!--<p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Key licence expired</b></p>-->
      <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Unable to convert the value of the 'result' column to an integer. Ensure all values are numeric</b></p>
      </div>
            </div>
          </div>
            <div *ngIf="item.type == 'bar'" class="clickable col-md-6"  style="display: contents" [hidden]="item.widgetDetails.Type != 'barFFR'" >
              <div class="chartjs-container" >
                <canvas baseChart
                [datasets]="item.data"
                [labels]="item.label"
                [chartType]="item.type"
               
                [options]="item.option">
                </canvas>
              </div>
          </div>
          <div *ngIf="item.type == 'countFFR'" class="clickable col-md-6"  style="display: contents" [hidden]="item.widgetDetails.Type != 'countFFR'" >
            <div class="brand-card1" style="padding: 0px !important;">
              <div class="count-box1">
                <div class="count-title11"><u>{{item.option}}</u></div>
                <div class="count-title22">{{item.data}}</div>
              </div>
            </div>
        </div>

            <div [hidden]="item.widgetDetails.Type != 'table' && item.widgetDetails.Type != 'tableFFR'">
             <!--<h5 style="text-align: center;">{{item.option}}</h5>-->
             <div *ngIf="item.conferror"> 
              <div class="box-header with-border" style="padding-top: 20px;text-align: center;">
    <!--<p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Key licence expired</b></p>-->
    <p class="description-text" style="font-size: 0.8rem; color: rgb(252, 35, 35);"><b>Unable to convert the value of the 'result' column to an integer. Ensure all values are numeric</b></p>
    </div>
          </div>
          <div *ngIf="!item.conferror"> 
                      <div>{{nodataTable}}</div>
             <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData"
              [scrollable]="item.tableData && item.tableData.length > 1 ? true : false" scrollHeight="400px" styleClass="p-datatable-sm">
                <ng-template pTemplate="header" let-columns>
                    <tr class="stickyHeader" style="white-space: nowrap; font-size: small;" >
                        <th *ngFor="let col of columns" style="text-align:center;">
                          {{col.header}}
                        </th>
                      </tr>
                </ng-template>
                <ng-template pTemplate="body" let-rowData let-columns="columns">
                    <tr style="font-size: small;">
                      <ng-container *ngFor="let col of columns">
                      <td style="text-align:center">
                        <span >{{rowData[col.header]}}</span>
                      </td>
                    </ng-container>
                    </tr>
                  </ng-template>
                  
             </p-table>
            </div>
               <!-- *ngIf="detailsDataUp.showHistoryView"<table class="table table-hover fuseqDataTable fuseqTableHeaderDynamic" id="historyTable" datatable [dtOptions]="dtOptionsHistory" *ngIf="dtRenderedHistory">
                  </table>-->
              </div>
              <div class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'summaryDays'">
                <div>{{nodataTable}}</div>
       <p-table  [value]="item.tableData" [columns]="item.colsTable" scrollHeight="flex"  #indicator 
        [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" 
        [globalFilterFields]="getGlobalFilterFields()"  sortMode="multiple" [multiSortMeta]="[{field: 'indicator name', order: 1}]">
        <ng-template pTemplate="caption">
         <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
           <div>
             <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
           Count: {{indicator.filteredValue?indicator.filteredValue.length:indicator.totalRecords}}
           </div>
             <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
               <div class="input-group">
                 <div class="input-group-prepend">
                 </div>
                 <div  class="gridster-item-content">
                 <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search" pInputText size="30" (input)="indicator.filterGlobal($event.target.value, 'contains'); indicator.resetScrollTop()" >
              </div> </div>
             </div>
         </div>
       </ng-template>
       <ng-template pTemplate="header" let-columns>
         <tr class="stickyHeader" style="white-space: nowrap;">
           <th *ngFor="let col of item.colsTable"  pSortableColumn="{{col.header}}" [hidden]="col.header.includes('compliant')" style="text-align: center;">
             {{ col.header }} <p-sortIcon field="{{col.header}}"></p-sortIcon>        
           </th>
         </tr>
         <tr *ngIf="item.showColFilters">
           <th *ngFor="let col of item.colsTable" [hidden]="col.header.includes('compliant')" style="text-align: center; background-color: white !important;">
             <div class="gridster-item-content">
               <input 
                 style="width:100%; text-align:center" 
                 pInputText 
                 [value]="indicator1.filters[col.header]?.value || ''"
                 type="search" 
                 class="form-control" 
                 #textInput 
                 (input)="indicator1.filter(applyFilterGlobal($event), col.header, 'contains'); indicator1.resetScrollTop()">
             </div>
           </th>
         </tr>
       </ng-template>        
       <ng-template pTemplate="body" let-rowData let-columns="columns">
         <tr>
           <ng-container *ngFor="let col of item.colsTable">
             <td [hidden]="col.header.includes('compliant')" 
                 [ngClass]="{'compliant': rowData[col.header + '_compliant'] === true,'notcompliant': rowData[col.header + '_compliant'] === false, 'null': rowData[col.header + '_compliant'] === null }" style="text-align: center;min-width: 150px">
               <span [tooltip]="rowData[col.header] || rowData[col.header]" class="truncateWidget" data-container="body">
                 {{ rowData[col.header] || rowData[col.header] }}
               </span>
             </td>
           </ng-container>
         </tr>
       </ng-template>  
            <ng-template pTemplate="emptymessage">
             <td style="text-align:center"  colspan="10">No data</td>
       </ng-template>
       </p-table>
        </div>
        <div class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'summaryWeek'">
         <div>{{nodataTable}}</div>
<p-table  [value]="item.tableData" [columns]="item.colsTable" scrollHeight="flex"  #indicator 
 [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" 
 [globalFilterFields]="getGlobalFilterFields()"  sortMode="multiple" [multiSortMeta]="[{field: 'indicator name', order: 1}]">
 <ng-template pTemplate="caption">
  <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
    <div>
      <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
    Count: {{indicator.filteredValue?indicator.filteredValue.length:indicator.totalRecords}}
    </div>
      <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
        <div class="input-group">
          <div class="input-group-prepend">
          </div>
          <div  class="gridster-item-content">
          <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search" pInputText size="30" (input)="indicator.filterGlobal($event.target.value, 'contains'); indicator.resetScrollTop()" >
       </div> </div>
      </div>
  </div>
</ng-template>
<ng-template pTemplate="header" let-columns>
 <tr class="stickyHeader" style="white-space: nowrap;">
   <th *ngFor="let col of item.colsTable"  pSortableColumn="{{col.header}}" [hidden]="col.header.includes('compliant')" style="text-align: center;">
     {{ col.header }} <p-sortIcon field="{{col.header}}"></p-sortIcon>        
   </th>
 </tr>
 <tr *ngIf="item.showColFilters">
   <th *ngFor="let col of item.colsTable" [hidden]="col.header.includes('compliant')" style="text-align: center; background-color: white !important;">
     <div class="gridster-item-content">
       <input 
         style="width:100%; text-align:center" 
         pInputText 
         [value]="indicator1.filters[col.header]?.value || ''"
         type="search" 
         class="form-control" 
         #textInput 
         (input)="indicator1.filter(applyFilterGlobal($event), col.header, 'contains'); indicator1.resetScrollTop()">
     </div>
   </th>
 </tr>
</ng-template>        
<ng-template pTemplate="body" let-rowData let-columns="columns">
 <tr>
   <ng-container *ngFor="let col of item.colsTable">
     <td [hidden]="col.header.includes('compliant')" 
         [ngClass]="{'compliant': rowData[col.header + '_compliant'] === true,'notcompliant': rowData[col.header + '_compliant'] === false, 'null': rowData[col.header + '_compliant'] === null }" style="text-align: center;min-width: 150px">
       <span [tooltip]="rowData[col.header] || rowData[col.header]" class="truncateWidget" data-container="body">
         {{ rowData[col.header] || rowData[col.header] }}
       </span>
     </td>
   </ng-container>
 </tr>
</ng-template>  
     <ng-template pTemplate="emptymessage">
      <td style="text-align:center"  colspan="10">No data</td>
</ng-template>
</p-table>
 </div>
        <div class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'summaryMonth'">
         <div>{{nodataTable}}</div>
<p-table  id="indicator1"  class="table table-hover evidenceDataTable paramTable" #indicator1  [columns]="item.colsTable"  [value]="item.tableData" scrollHeight="flex" 
 [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" 
 [globalFilterFields]="getGlobalFilterFields()"   sortMode="multiple" [multiSortMeta]="[{field: 'indicator name', order: 1}]">
 <ng-template pTemplate="caption">
  <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
    <div>
      <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
    Count: {{indicator1.filteredValue?indicator1.filteredValue.length:indicator1.totalRecords}}
    </div>
      <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
        <div class="input-group">
          <div class="input-group-prepend">
          </div>   <div  class="gridster-item-content">
          <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search" pInputText size="30" (input)="indicator1.filterGlobal($event.target.value, 'contains'); indicator1.resetScrollTop()" >
     </div>   </div>
      </div>
  </div>
</ng-template>
<!--   <ng-template pTemplate="header" let-columns>
    <tr class="stickyHeader" style="white-space: nowrap;">
      <th style="text-align:center" *ngFor="let col of columns" pSortableColumn="{{col.header}}">
        {{col.header}} <p-sortIcon field="{{col.header}}"></p-sortIcon>                       
      </th>
    </tr>
    <tr *ngIf=" item.showColFilters" class="scrollFilterTree" >
      <th  *ngFor="let col of columns" style="background-color: white !important">
       <div  class="gridster-item-content"><input style="width:100%"  pInputText 
        style=" text-align:center"  colpsan= "14"

         [value]="indicator1.filters[col.header] ? indicator1.filters[col.header].value : ''"
         type="search" class="form-control" #textInput (input)="indicator1.filter(applyFilterGlobal($event), col.header, 'contains'); indicator1.resetScrollTop()">
   </div>   

       </th>
     </tr>                   
   </ng-template>
   <ng-template pTemplate="body" let-rowData let-columns="columns">
       <tr style="font-size: small;">
        <ng-container *ngFor="let col of columns">
         <td style="text-align:center">
           <span [tooltip] = "rowData[col.header]"  class="truncateWidget" data-container="body" >{{rowData[col.header]}}</span> 
         </td>
       </ng-container>           
       </tr>
     </ng-template>  
     <ng-template pTemplate="emptymessage">
       <td style="text-align:center"  colspan="10">No data</td>
 </ng-template>--> 
 <ng-template pTemplate="header" let-columns>
   <tr class="stickyHeader" style="white-space: nowrap;">
     <th *ngFor="let col of item.colsTable"  pSortableColumn="{{col.header}}" [hidden]="col.header.includes('compliant')" style="text-align: center;">
       {{ col.header }} <p-sortIcon field="{{col.header}}"></p-sortIcon>        
     </th>
   </tr>
   <tr *ngIf="item.showColFilters">
     <th *ngFor="let col of item.colsTable" [hidden]="col.header.includes('compliant')" style="text-align: center; background-color: white !important;">
       <div class="gridster-item-content">
         <input 
           style="width:100%; text-align:center" 
           pInputText 
           [value]="indicator1.filters[col.header]?.value || ''"
           type="search" 
           class="form-control" 
           #textInput 
           (input)="indicator1.filter(applyFilterGlobal($event), col.header, 'contains'); indicator1.resetScrollTop()">
       </div>
     </th>
   </tr>
 </ng-template>        
 <ng-template pTemplate="body" let-rowData let-columns="columns">
   <tr>
     <ng-container *ngFor="let col of item.colsTable">
       <td [hidden]="col.header.includes('compliant')" 
           [ngClass]="{'compliant': rowData[col.header + '_compliant'] === true,'notcompliant': rowData[col.header + '_compliant'] === false, 'null': rowData[col.header + '_compliant'] === null }" style="text-align: center;min-width: 150px;">
         <span [tooltip]="rowData[col.header] || rowData[col.header]" class="truncateWidget" data-container="body">
           {{ rowData[col.header] || rowData[col.header] }}
         </span>
       </td>
     </ng-container>
   </tr>
 </ng-template>                
</p-table>
 </div>
        <div class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'summaryQuarter'">
         <div>{{nodataTable}}</div>
<p-table  [value]="item.tableData" [columns]="item.colsTable" scrollHeight="flex"  #indicator2
 [scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" 
 [globalFilterFields]="getGlobalFilterFields()" sortMode="multiple" [multiSortMeta]="[{field: 'indicator name', order: 1}]">
 <ng-template pTemplate="caption">
  <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
    <div>
      <button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
    Count: {{indicator2.filteredValue?indicator2.filteredValue.length:indicator2.totalRecords}}
    </div>
      <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
        <div class="input-group">
          <div class="input-group-prepend">
          </div>
          <div  class="gridster-item-content">
          <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search" pInputText size="30" (input)="indicator2.filterGlobal($event.target.value, 'contains'); indicator2.resetScrollTop()" >
         </div></div>

      </div>
  </div>
</ng-template>
<ng-template pTemplate="header" let-columns>
 <tr class="stickyHeader" style="white-space: nowrap;">
   <th *ngFor="let col of item.colsTable"  pSortableColumn="{{col.header}}" [hidden]="col.header.includes('compliant')" style="text-align: center;">
     {{ col.header }} <p-sortIcon field="{{col.header}}"></p-sortIcon>        
   </th>
 </tr>
 <tr *ngIf="item.showColFilters">
   <th *ngFor="let col of item.colsTable" [hidden]="col.header.includes('compliant')" style="text-align: center; background-color: white !important;">
     <div class="gridster-item-content">
       <input 
         style="width:100%; text-align:center" 
         pInputText 
         [value]="indicator1.filters[col.header]?.value || ''"
         type="search" 
         class="form-control" 
         #textInput 
         (input)="indicator1.filter(applyFilterGlobal($event), col.header, 'contains'); indicator1.resetScrollTop()">
     </div>
   </th>
 </tr>
</ng-template>        
<ng-template pTemplate="body" let-rowData let-columns="columns">
 <tr>
   <ng-container *ngFor="let col of item.colsTable">
     <td [hidden]="col.header.includes('compliant')" 
         [ngClass]="{'compliant': rowData[col.header + '_compliant'] === true,'notcompliant': rowData[col.header + '_compliant'] === false, 'null': rowData[col.header + '_compliant'] === null }" style="text-align: center;min-width: 150px;">
       <span [tooltip]="rowData[col.header] || rowData[col.header]" class="truncateWidget" data-container="body">
         {{ rowData[col.header] || rowData[col.header] }}
       </span>
     </td>
   </ng-container>
 </tr>
</ng-template>  
     <ng-template pTemplate="emptymessage">
       <td style="text-align:center"  colspan="10">No data</td>
 </ng-template>
</p-table>
 </div>
 <div class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'summarySemester'">
   <div>{{nodataTable}}</div>
<p-table  [value]="item.tableData" [columns]="item.colsTable" scrollHeight="flex"  #indicator3 
[scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" 
[globalFilterFields]="getGlobalFilterFields()" sortMode="multiple" [multiSortMeta]="[{field: 'indicator name', order: 1}]">
<ng-template pTemplate="caption">
<div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
<div>
<button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
Count: {{indicator3.filteredValue?indicator3.filteredValue.length:indicator3.totalRecords}}
</div>
<div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
  <div class="input-group">
    <div class="input-group-prepend">
    </div>  <div  class="gridster-item-content">
    <input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search" pInputText size="30" (input)="indicator3.filterGlobal($event.target.value, 'contains'); indicator3.resetScrollTop()" >
</div>  </div>
</div>
</div>
</ng-template>
<ng-template pTemplate="header" let-columns>
<tr class="stickyHeader" style="white-space: nowrap;">
<th *ngFor="let col of item.colsTable"  pSortableColumn="{{col.header}}" [hidden]="col.header.includes('compliant')" style="text-align: center;">
{{ col.header }} <p-sortIcon field="{{col.header}}"></p-sortIcon>        
</th>
</tr>
<tr *ngIf="item.showColFilters">
<th *ngFor="let col of item.colsTable" [hidden]="col.header.includes('compliant')" style="text-align: center; background-color: white !important;">
<div class="gridster-item-content">
 <input 
   style="width:100%; text-align:center" 
   pInputText 
   [value]="indicator1.filters[col.header]?.value || ''"
   type="search" 
   class="form-control" 
   #textInput 
   (input)="indicator1.filter(applyFilterGlobal($event), col.header, 'contains'); indicator1.resetScrollTop()">
</div>
</th>
</tr>
</ng-template>        
<ng-template pTemplate="body" let-rowData let-columns="columns">
<tr>
<ng-container *ngFor="let col of item.colsTable">
<td [hidden]="col.header.includes('compliant')" 
   [ngClass]="{'compliant': rowData[col.header + '_compliant'] === true,'notcompliant': rowData[col.header + '_compliant'] === false, 'null': rowData[col.header + '_compliant'] === null }" style="text-align: center;min-width: 150px">
 <span [tooltip]="rowData[col.header] || rowData[col.header]" class="truncateWidget" data-container="body">
   {{ rowData[col.header] || rowData[col.header] }}
 </span>
</td>
</ng-container>
</tr>
</ng-template>  
<ng-template pTemplate="emptymessage">
 <td style="text-align:center"  colspan="10">No data</td>
</ng-template>
</p-table>
</div>
<div class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'summaryYear'">
<div>{{nodataTable}}</div>
<p-table  [value]="item.tableData" [columns]="item.colsTable" scrollHeight="flex"  #indicator4 
[scrollable]="item.tableData && item.tableData.length > 1 ? true : false"  styleClass="p-datatable-sm" 
[globalFilterFields]="getGlobalFilterFields()"  sortMode="multiple" [multiSortMeta]="[{field: 'indicator name', order: 1}]">
<ng-template pTemplate="caption">
<div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
<div>
<button class="btn btn-outline-dark ml-2" (click)=" item.showColFilters = ! item.showColFilters"><i *ngIf=" item.showColFilters" class=" pi pi-filter-slash"></i><i *ngIf="! item.showColFilters" class=" pi pi-filter"></i></button>
Count: {{indicator4.filteredValue?indicator4.filteredValue.length:indicator4.totalRecords}}
</div>
<div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
<div class="input-group">
<div class="input-group-prepend">
</div>
<div  class="gridster-item-content">
<input type="search" class="form-control"  style="width: auto; max-width: 150px;" placeholder="Search" pInputText size="30" (input)="indicator4.filterGlobal($event.target.value, 'contains'); indicator4.resetScrollTop()" >
</div></div>
</div>
</div>
</ng-template>
<ng-template pTemplate="header" let-columns>
<tr class="stickyHeader" style="white-space: nowrap;">
<th *ngFor="let col of item.colsTable"  pSortableColumn="{{col.header}}" [hidden]="col.header.includes('compliant')" style="text-align: center;">
{{ col.header }} <p-sortIcon field="{{col.header}}"></p-sortIcon>        
</th>
</tr>
<tr *ngIf="item.showColFilters">
<th *ngFor="let col of item.colsTable" [hidden]="col.header.includes('compliant')" style="text-align: center; background-color: white !important;">
<div class="gridster-item-content">
<input 
 style="width:100%; text-align:center" 
 pInputText 
 [value]="indicator1.filters[col.header]?.value || ''"
 type="search" 
 class="form-control" 
 #textInput 
 (input)="indicator1.filter(applyFilterGlobal($event), col.header, 'contains'); indicator1.resetScrollTop()">
</div>
</th>
</tr>
</ng-template>        
<ng-template pTemplate="body" let-rowData let-columns="columns">
<tr>
<ng-container *ngFor="let col of item.colsTable">
<td [hidden]="col.header.includes('compliant')" 
 [ngClass]="{'compliant': rowData[col.header + '_compliant'] === true,'notcompliant': rowData[col.header + '_compliant'] === false, 'null': rowData[col.header + '_compliant'] === null }" style="text-align: center;min-width: 150px">
<span [tooltip]="rowData[col.header] || rowData[col.header]" class="truncateWidget" data-container="body">
 {{ rowData[col.header] || rowData[col.header] }}
</span>
</td>
</ng-container>
</tr>
</ng-template>   
<ng-template pTemplate="emptymessage">
<td style="text-align:center"  colspan="10">No data</td>
</ng-template>
</p-table>
</div>

            </gridster-item>
        </gridster>
       
    </div>



   
    </div>
</div>


<p-dialog [ngClass]="dialogClass==='pdialogCreate' ? 'pdialogcreate' : dialogClass==='pdialogEdit' ? 'pdialogedit' : 'pdialogview'" [(visible)]="variabile" [modal]="true" [style]="{width: '50vw'}" [maximizable]="false" [baseZIndex]="10000"
    [draggable]="true" [resizable]="true" #dialDetail (onHide)="closeDashboard()">
    <ng-template pTemplate="header" > {{dashboardTitle}}  <button style="margin-right: 92%;color: white;" [hidden]="dashboardType !== 'View'" class="btn btn-sm btn-link" (click)="openViewEdit()">
        <i class="fa fa-pencil"></i>
    </button></ng-template>

        <div  style="display: flex;height: 100%;">
        <gridster [options]="options"
        [ngStyle]="dashboardType == 'View' ?  {'background':'#ffffff'} : ''">
            <gridster-item [item]="item" [ngStyle]="dashboardType == 'View' ?  {'border': '1px solid #2a5d89','border-radius': '0.25rem'} : ''" *ngFor="let item of dashboard; let i = index">
                <div class="button-holder">
                    <div class="gridster-item-content">
                        <div class="item-buttons">
                        <button [hidden]="hideDetailsOnView == true" class="btn btn-sm btn-grey float-right" (click)="removeItem(i)">
                            <i class="fa fa-trash"></i>
                        </button>
                        <div  class="btn-group float-right" dropdown container="gridster" [hidden]="hideDetailsOnView == true" >
                            <button id="button-basic" dropdownToggle type="button" class="btn btn-gray btn-sm dropdown-toggle"
                            aria-controls="dropdown-basic">
                            <i class="fa fa-cog"></i> <span class="caret"></span>
                            </button>
                            <ul id="dropdown-basic" *dropdownMenu class="dropdown-menu"
                            role="menu" aria-labelledby="button-basic">
                            <li role="menuitem" *ngIf="!item.checkshared" ><a class="dropdown-item" (click)="cloneWidget(item)">Clone</a></li>
                            <li role="menuitem"><a class="dropdown-item" (click)="showWidgetModal(i,'Edit')">Edit</a></li>
                            <!--<li class="divider dropdown-divider"></li>
                            <li role="menuitem">
                            <a class="dropdown-item" (click)="showWidgetModal()">Visualizza</a>
                            </li>-->
                            </ul>
                            </div>
                        </div>
                        <i [ngClass]="item.icon"></i>

                    </div>
                </div>

            <div class="col sm-4" id="count" style="display: contents;" [hidden]="isSaved == false || item.widgetDetails.Type != 'count'">
                <div class="clickable"  style="display: flex;" data-toggle="modal" >

                    <!--"dialogClass==='pdialogCreate' ? 'pdialogcreate' : dialogClass==='pdialogEdit' ? 'pdialogedit' : 'pdialogview'-->
                  <div class="callout callout-info "
                  [ngStyle]="dialogClass==='pdialogCreate' ? {'width': '150px', 'border-left-color':'#2b897a'} :
                  dialogClass==='pdialogEdit' ? {'width': '150px', 'border-left-color':'#fe9800'} :
                  {'width': '150px', 'border-left-color':'#2196f3'}">
                    <small class="text-muted"style="white-space:nowrap">Count - {{item.option}}</small>
                            <br>
                            <strong class="h4">{{item.data}}</strong>
                  </div>
                 </div>
              </div>

              <div *ngIf="item.type == 'pie'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'pie'">
                <div class="chartjs-container"  >
                  <canvas
                  baseChart
                  [data]="item.data"
                  [labels]="item.label"
                  [chartType]="item.type"
                  [colors]=item.color
                  [options]="item.option"
                  >
                  </canvas>
                </div>
             </div>
             <div *ngIf="item.type == 'doughnut'" class="clickable sm-4"  style="display: contents;" [hidden]="item.widgetDetails.Type != 'doughnut'">
                <div class="chartjs-container"  >
                  <canvas
                  baseChart
                  [data]="item.data"
                  [labels]="item.label"
                  [chartType]="item.type"
                  [colors]=item.color
                  [options]="item.option"
                  >
                  </canvas>
                </div>
             </div>
             <div *ngIf="item.type == 'line'" class="clickable sm-4"  style="display: contents;" >
             <div class="chartjs-container" id="line-chart"  [hidden]="item.widgetDetails.Type != 'line'">
              <canvas
              baseChart
              [datasets]="item.data"
              [labels]="item.label"

              [colors]="item.color"
              [chartType]="item.type"
              [options]="item.option"
              >
             </canvas>
                </div>
             </div>
             <div *ngIf="item.type == 'bar'" class="clickable col-md-6"  style="display: contents" [hidden]="item.widgetDetails.Type != 'bar'" >
                <div class="chartjs-container">
                 <canvas baseChart
                    [datasets]="item.data"
                    [labels]="item.label"
                    [chartType]="item.type"
                    [colors]="item.color"
                    [options]="item.option">
                 </canvas>
                </div>
            </div>

            <div *ngIf="item.type == 'bar'" class="clickable col-md-6"  style="display: contents" [hidden]="item.widgetDetails.Type != 'barFFR'" >
              <div class="chartjs-container" >
               <canvas baseChart
               [datasets]="item.data"
               [labels]="barFFRChartLabels"
               [chartType]="item.type"
              
               [options]="barFFRChartOptions">
               </canvas>
              </div>
          </div>

            <div [hidden]="item.widgetDetails.Type != 'table' && item.widgetDetails.Type != 'tableFFR'">
             <h5 style="text-align: center;">{{item.option}}</h5>
             <p-table [columns]="item.colsTable"  #pt class="pt" [value]="item.tableData"
              [scrollable]="item.tableData && item.tableData.length > 1 ? true : false" scrollHeight="400px" styleClass="p-datatable-sm">
                <ng-template pTemplate="header" let-columns>
                    <tr class="stickyHeader" style="white-space: nowrap; font-size: small;" >
                        <th *ngFor="let col of columns" style="text-align:center;">
                          {{col.header}}
                        </th>
                      </tr>
                </ng-template>
                <ng-template pTemplate="body" let-rowData let-columns="columns">
                    <tr style="font-size: small;">
                      <ng-container *ngFor="let col of columns">
                      <td style="text-align:center">
                        <span >{{rowData[col.header]}}</span>
                      </td>
                    </ng-container>
                    </tr>
                  </ng-template>
                  
             </p-table>
               <!-- *ngIf="detailsDataUp.showHistoryView"<table class="table table-hover fuseqDataTable fuseqTableHeaderDynamic" id="historyTable" datatable [dtOptions]="dtOptionsHistory" *ngIf="dtRenderedHistory">
                  </table>-->
              </div>
            </gridster-item>
        </gridster>
        <div style="background: #ffffff;max-width: 150px;height: 100%;" class="getElem">
            <p-panelMenu [hidden]="hideDetailsOnView == true" [model]="items" [style]="{'width':'300px'}">

            </p-panelMenu>
            <div style="padding: 1rem; display: flex;" [hidden]="hideDetailsOnView == true">
                <!-- (click)="displayMaximizable=false" - Cancel button original method -->
                <button (click)="saveDashboard(displayMaximizable)" label="Yes"
                [ngClass]="dialogClass==='pdialogCreate' ? 'btn btn-success' : 'btn btn-warning'">Save</button>
                <button (click)="closeDashboard()" label="No" class="btn btn-danger ml-1">Cancel</button>
            </div>
        </div>
    </div>
</p-dialog>

<div id="addModal" bsModal #addModal="bs-modal" class="modal fade bs-example-modal-lg" role="dialog" aria-labelledby="classInfo"
    aria-hidden="true" style="z-index: 100000;" [config]="{backdrop: 'static',  keyboard: false}">
  <div class="modal-dialog modal-lg">
   <div class="modal-content">
     <div [ngClass]="typeModalWidget==='New' ? 'modal-header modalHeaderCreate' : 'modal-header modalHeaderModify'">
         <h5 class="modal-title uc" id="approveModalLabel">{{widgetTitle}}</h5>
         <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="cancelWidgetModal()">
            <span aria-hidden="true">&times;</span>
          </button>
     </div>
     <div class="modal-body marginUnset fuseqGreyBack">
        <div class="fuseqTabset-modal">
          <tabset #tabCreate>
            <tab #tab1 heading="Details">
                <div class="form-group" style="display: flex;">
                    <div class="input-group mr-2">
                        <div class="input-group-prepend" ><span class="input-group-text" style="min-width: 95px;">Name</span></div>
                        <input [disabled]="hideDetailsOnView == true || itemDetails.checkshared" type="text" class="form-control" placeholder="Name" [(ngModel)]="widget.name">
                    </div>
                    <div class="input-group mr-2">
                        <div class="input-group-prepend" ><span class="input-group-text" style="min-width: 95px;">Description</span></div>
                        <input [disabled]="hideDetailsOnView == true || itemDetails.checkshared" type="text" class="form-control" placeholder="Description" [(ngModel)]="widget.description" >
                    </div>
                </div>
                <div class="form-group" style="display: flex;">
                    <div class="input-group mr-2">
                        <div class="input-group-prepend" ><span class="input-group-text" style="min-width: 95px;">Active</span></div>
                        <label class="switch switch-label switch-outline-success-alt ml-3"><input [disabled]="hideDetailsOnView == true || itemDetails.checkshared" checked=""  class="switch-input" type="checkbox" [(ngModel)]="widget.active"><span class="switch-slider" data-checked="✓" data-unchecked="✕" ></span></label>
                    </div>
                    <div class="input-group mr-2">
                        <div class="input-group-prepend" ><span class="input-group-text" style="min-width: 95px;">Shared</span></div>
                        <label class="switch switch-label switch-outline-success-alt ml-3" *ngIf="itemDetails.checkshared"><input [disabled]="true"  [checked]="true" class="switch-input" type="checkbox"><span class="switch-slider" data-checked="✓" data-unchecked="✕" ></span></label>
                        <label class="switch switch-label switch-outline-success-alt ml-3" *ngIf="!itemDetails.checkshared"><input [disabled]="hideDetailsOnView == true || itemDetails.checkshared" checked=""  class="switch-input" type="checkbox" [(ngModel)]="widget.shared"><span class="switch-slider" data-checked="✓" data-unchecked="✕" ></span></label>
                    </div>
                </div>
            </tab>
            <tab #tab2 heading="Data" (selectTab)="filterFFRActiveTab(selectedFFR)">
                <div  class="form-group"  style="display: flex" *ngIf="typeWidget == 'tableFFR' || typeWidget == 'barFFR' || typeWidget == 'countFFR'">
                    <div  class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;" >Select a FFR</span></div>
                    <select class="form-control"  [(ngModel)]="selectedFFR" (change)="filterFFR($event)"   >
                        <option *ngFor="let row of ffrList" value="{{row.id}}">{{row.name}}</option>
                    </select>

                </div>
                <div class="form-group" *ngIf="typeWidget == 'tableFFR' || typeWidget == 'barFFR' || typeWidget == 'countFFR'" >
                    <div *ngFor="let param of arrayParams" class="form-group" style="display: flex;">
                        <div class="input-group mr-2">
                        <div class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Name</span></div>
                          <input type="text" class="form-control" [(ngModel)]="param.name" disabled >
                        </div>
                        <div class="input-group mr-2">
                        <div class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Value</span></div>
                          <input type="text" class="form-control" [(ngModel)]="param.value">
                        </div>
                        <div class="input-group mr-2">
                          <div class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Type</span></div>
                          <select type="text" class="form-control" placeholder="type" [(ngModel)]="param.type" [ngClass]="param.disable ? 'is-invalid':''">
                          <option value="text">text</option>
                          <option value="integer">integer</option>
                        </select>
                        <div class="invalid-feedback" *ngIf="param.disable">{{typeMessage}}</div>
                        </div>
                      </div>
                </div>
              <!--  <div class="form-group" *ngIf="typeWidget == 'lineFFR' || typeWidget == 'barFFR' || typeWidget == 'pieFFR' || typeWidget == 'doughnutFFR'">
                    <div class="input-group">
                      <input class="ml-3 mt-1" type="radio" [value]="'query'" name="newquery" [(ngModel)]="ffrtypequery"  />
                      <label style="display: unset; margin-bottom: unset;" class="ml-1">Query</label>
                      <br>
                      <input class="ml-4 mt-1" type="radio" [value]="'data'" name="newquery" [(ngModel)]="ffrtypequery"  />
                      <label style="display: unset; margin-bottom: unset;" class="ml-1">Data</label>
                    </div>

                  </div>
                <div class="form-group" style="display: flex;" *ngIf="ffrtypequery!=='data'">
                    <div class="input-group mr-2">
                      <div class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Query</span></div>
                      <textarea class="form-control" id="addffrarea" rows="5" [(ngModel)]="ffr.config" [ngClass]="Querydisable ? 'is-invalid':''">
                      </textarea>
                      <div class="invalid-feedback"  *ngIf="Querydisable">{{queryMessage}}</div>
                    </div>
                  </div>-->
                  <div *ngIf="ffrtypequery=='data'" >
                <div  class="form-group"  style="display: flex" *ngIf="typeWidget != 'tableFFR'  && typeWidget != 'barFFR' || typeWidget == 'countFFR'">
                    <div  class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Select a Class</span></div>
                    <select class="form-control" [disabled]="itemDetails.checkshared" [(ngModel)]="selectedQueryClass" (ngModelChange)="getClassDetails(selectedQueryClass)">
                        <option *ngFor="let row of array_distinct" value="{{row}}">{{row}}</option>
                    </select>
                    <div class="input-group-prepend ml-3" ><span id="actions" class="input-group-text" style="max-width: 95px; ">Join</span></div>
                    <div class="input-group" >
                        <p-multiSelect class="multiselect-custom" [disabled]="itemDetails.checkshared" [options]="joinClasses" [(ngModel)]="joinClassesSelected" (ngModelChange)="setJoinAttributes(joinClassesSelected)" optionLabel="Code"  defaultLabel="Select a class"  optionValue="Id">
                            <ng-template let-value pTemplate="selectedItems">
                              <div class="important" *ngFor="let option of joinClassesSelected">
                                  <div class ="custom-option" *ngIf="joinClassesSelected!==[] && joinClassesSelected.length>0" >{{joinClassesSelected.length}} selected</div>
                              </div>
                                  Select Join Classes
                          </ng-template>
                        </p-multiSelect>
                        </div>
                    </div>
                    <div *ngIf="selectedQueryClass != null && typeWidget != 'count' && typeWidget == 'table'" class="form-group"  style="display: flex">
                        <div  class="input-group-prepend">
                            <span class="input-group-text" style="min-width: 95px;">Select one or more columns field</span></div>
                            <p-multiSelect class="multiselect-custom"  [disabled]="itemDetails.checkshared"
                            [options]="columnsQuery" [(ngModel)]="selectedColumns"
                            optionLabel="Name" defaultLabel="Select a field" optionValue="Name">

                            </p-multiSelect>
                    </div>
                    <div *ngIf="selectedQueryClass != null && typeWidget != 'count' && typeWidget != 'table'" class="form-group"  style="display: flex">
                        <div class="input-group-prepend">
                            <span class="input-group-text" style="min-width: 95px;">Select one column field</span></div>
                        <select class="form-control" [(ngModel)]="selectedColumns[0]" [disabled]="itemDetails.checkshared">
                            <option *ngFor="let row of columnsQuery" [value]="row.Name">{{row.Name}}</option>
                        </select>
                    </div>
                    <div  *ngFor="let item of aggregationSet; let i = index">
                        <p-fieldset *ngIf="selectedQueryClass != null" [toggleable]="true">
                        <div *ngIf="selectedQueryClass != null">
                            <div class="form-group"  style="display: flex">
                            <div *ngIf=" typeWidget != 'count'" class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Select an aggregation</span></div>
                            <select *ngIf=" typeWidget != 'count'" class="form-control"  [disabled]="itemDetails.checkshared"
                            [(ngModel)]="item.aggregation" (change)="changeAggregation($event, i)">
                                <option value=""></option>
                                <option value="sum">Sum</option>
                                <option value="count">Count</option>
                                <option value="avg">Average</option>
                                <option value="max">Maximum</option>
                                <option value="min">Minimum</option>
                            </select>
                            <select *ngIf="typeWidget != 'count'" class="form-control" [(ngModel)]="item.case"
                            [disabled]="itemDetails.checkshared" (change)="changeCase($event, i)">
                                <option value=""></option>
                                <option value="case when">CASE WHEN</option>
                            </select></div>
                            <div  class="form-group"  style="display: flex">
                            <select *ngIf="!item.casedisable" class="form-control" name="item.caseColumn"
                            [disabled]="itemDetails.checkshared" [(ngModel)]="item.caseColumn" (change)="changeCaseColumn($event, i)" >
                                <option value=""></option>
                                <option *ngFor="let row of columnsQuery"
                                [value]="row.Name" >{{row.Name}}</option>
                            </select>

                                <select  *ngIf="!item.casedisable" class="form-control"
                                [disabled]="itemDetails.checkshared" name="item.operator" [(ngModel)]="item.operator">
                                    <option value=""></option>
                                    <option  *ngFor="let row of item.operators" [value]="row">{{row}}</option>
                                </select>
                                <input *ngIf="!item.casedisable && (item.columnType == '' || item.columnType == 'text' || item.columnType == 'numeric')"  [disabled]="itemDetails.checkshared" type="text" class="form-control" [(ngModel)]="item.valuecase">
                                <input *ngIf="!item.casedisable && item.columnType == 'timestamp'"  [disabled]="itemDetails.checkshared" type="datetime-local" max="9999-12-31T23:59" class="form-control" [(ngModel)]="item.valuecase">
                                <select *ngIf="!item.casedisable && item.columnType == 'bool'"  [disabled]="itemDetails.checkshared" [(ngModel)]="item.valuecase">
                                    <option value=""></option>
                                    <option value="true">true</option>
                                    <option value="false">false</option>
                                </select>
                            </div>
                            <div  class="form-group"  style="display: flex">
                                <select disabled *ngIf=" typeWidget != 'count'" class="form-control" [(ngModel)]="item.then">
                                    <option value=""></option>
                                    <option value="then">THEN</option>
                                </select>
                                <span class="input-group-text" style="min-width: 95px;">Data field</span>
                                <select [disabled] = "item.disableField || itemDetails.checkshared" class="form-control" name="item.aggregationColumn" [(ngModel)]="item.aggregationColumn">
                                    <option value=""></option>
                                    <option *ngIf="item.aggregation == 'count' || typeWidget == 'count'" value="*">*</option>
                                    <option *ngFor="let row of item.columnsQueryAggr" [value]="row.Name">{{row.Name}}</option>
                                </select>
                                <div *ngIf="typeWidget != 'count' && i >= 1" style="display: flex;">
                                    <button *ngIf="selectedQueryClass != null" type="button"  [disabled]="itemDetails.checkshared"
                                    class="btn btn-danger" data-dismiss="modal" (click)="removeAggregation(i)">
                                        <i class="fa fa-minus"></i></button>
                                </div>
                            </div>
                       </div></p-fieldset>
                    </div>


                    <div *ngIf="typeWidget != 'count' && typeWidget != 'pie'" class="form-group" style="display: flex;">
                        <button *ngIf="selectedQueryClass != null" type="button"  [disabled]="itemDetails.checkshared"
                        class="btn btn-success" data-dismiss="modal" (click)="addAggregation()">
                            <i class="fa fa-plus"></i>Add aggregation</button>
                    </div>
                    <!--<div *ngIf="selectedQueryClass != null" class="form-group"  style="display: flex">
                        <div *ngIf=" typeWidget != 'count'" class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Select an aggregation</span></div>
                            <select *ngIf=" typeWidget != 'count'" class="form-control" [(ngModel)]="aggregation" (change)="changeAggregation($event)">
                            <option value=""></option>
                            <option value="sum">Sum</option>
                            <option value="count">Count</option>
                            <option value="avg">Average</option>
                            <option value="max">Maximum</option>
                            <option value="min">Minimum</option>
                        </select>
                        <span class="input-group-text" style="min-width: 95px;">Data field</span>
                        <select [disabled] = "dataFieldDisable" class="form-control" name="aggregationColumn" [(ngModel)]="aggregationColumn">
                            <option value=""></option>
                            <option  value="*">*</option>
                            <option *ngFor="let row of columnsQueryAggr" [value]="row.Name">{{row.Name}}</option>
                        </select>
                    </div>-->

                    <query-builder [disabled]="itemDetails.checkshared" *ngIf="selectedQueryClass != null" [(ngModel)]='trigger.query' [config]='config' [classNames]='classNames' (ngModelChange)="onChange($event, trigger.query)">
                      <ng-container *queryInput="let rule; type: 'timestamp'">
                        <div style="display: flex;">
                          <div *ngIf="!rule.isJoin" class="col-auto" style="display: flex; margin-top: 5px;">
                            <input type="datetime-local" class="form-control" [(ngModel)]="rule.value" (ngModelChange)="onChange($event, trigger.query)">
                          </div>
                          <div *ngIf="rule.isJoin" class="col-auto" style="display: flex; margin-top: 5px;">
                            <select class="form-control" [(ngModel)]="rule.value" (ngModelChange)="onChange($event, trigger.query)">
                              <option value="">Select a column field</option>
                              <option *ngFor="let column of columnsQuery" [value]="column.Name">
                                {{column.Name}}
                              </option>
                            </select>
                          </div>
                          <div class="col-auto" style="display: flex; margin-top: 5px;">
                            <span class="input-group-text" style="min-width: 35px;">Use join field</span>
                            <div style="border: 1px solid #ced4da;border-radius: 3px;">
                              <input type="checkbox" class="ml-3 mt-2 mr-3" [(ngModel)]="rule.isJoin" 
                                (change)="onRuleJoinChange(rule)">
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </query-builder>
                </div>
            </tab>
            <tab #tab3 heading="Properties" *ngIf="typeWidget != 'tableFFR' && typeWidget != 'barFFR' || typeWidget == 'countFFR'" (selectTab)="changeProperties()">
                <div class="form-group" style="display: flex;">
                    <div class="input-group mr-2">
                        <div class="input-group-prepend" ><span class="input-group-text" style="min-width: 95px;">Show legend</span></div>
                    <label class="switch switch-label switch-outline-success-alt ml-3"><input [disabled]="hideDetailsOnView == true || itemDetails.checkshared" checked=""  class="switch-input" type="checkbox" [(ngModel)]="widget.showlabel"><span class="switch-slider" data-checked="✓" data-unchecked="✕" ></span></label>
                    </div>
                </div>
                <p-table #ptalias class="ptalias" [value]="ListColumn">
                    <ng-template pTemplate="header" let-columns>
                        <tr class="stickyHeader" style="white-space: nowrap;">
                            <th>Column name</th>
                            <th>Alias</th>
                          </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-rowData>
                        <tr>
                            <td style="text-align:center">{{rowData.name}}</td>
                            <td style="text-align:center"><input type="text" [(ngModel)]="rowData.value" [disabled]="itemDetails.checkshared"></td>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="emptymessage">
                        <tr>
                            <td style="text-align:center;" [attr.colspan]="2">
                              Nessun dato
                            </td>
                        </tr>
                    </ng-template>
                 </p-table>
            </tab>
           <!-- <tab #tab4 heading="Parameters" *ngIf="ffrtypequery=='query'" style="text-align:end" (selectTab)="loadParams('new')" [disabled]= "ffrtypequery=='data'">
                <div *ngFor="let param of params" class="form-group" style="display: flex;">
                  <div class="input-group mr-2">
                  <div class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Name</span></div>
                    <input type="text" class="form-control" [(ngModel)]="param.name" disabled >
                  </div>
                  <div class="input-group mr-2">
                  <div class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Value</span></div>
                    <input type="text" class="form-control" [(ngModel)]="param.value">
                  </div>
                  <div class="input-group mr-2">
                    <div class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Type</span></div>
                    <select type="text" class="form-control" placeholder="type" [(ngModel)]="param.type" [ngClass]="param.disable ? 'is-invalid':''">
                    <option value="text">text</option>
                    <option value="integer">integer</option>
                  </select>
                  <div class="invalid-feedback" *ngIf="param.disable">{{typeMessage}}</div>
                  </div>
                </div>
              </tab>-->
          </tabset>
   </div>
   <div class="modal-footer">
        <button  [hidden]="typeWidget == 'barFFR'" [disabled]="hideDetailsOnView == true||isDisable == true"
        [ngClass]="typeModalWidget==='New' ? 'btn btn-success' : 'btn btn-warning'"
        (onclick)="isSaved=true" (click)="saveWidgetDetails(widget.Id)"  [disabled]="itemDetails.checkshared">Save</button>
        <button [hidden]="typeWidget != 'barFFR'" [disabled]="hideDetailsOnView == true||isDisable == true"
        [ngClass]="typeModalWidget==='New' ? 'btn btn-success' : 'btn btn-warning'"
        (onclick)="isSaved=true" (click)="checkValueChartFFR(widget.Id)"  [disabled]="itemDetails.checkshared">Save</button>
        <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="cancelWidgetModal()">Cancel</button>
  </div>
</div>
</div>
</div>
</div>



<div id="addDashboardModal" bsModal #addDashboardModal="bs-modal" class="modal fade bs-example-modal-lg" role="dialog" aria-labelledby="classInfo"
    aria-hidden="true" style="z-index: 100000;">
  <div class="modal-dialog modal-lg">
   <div class="modal-content">
     <div [ngClass]="dialogClass==='pdialogCreate' ? 'modal-header modalHeaderCreate' : 'modal-header modalHeaderModify'">
         <h5 class="modal-title uc" id="approveModalLabel">{{dashboardTitle}}</h5>
         <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="this.addDashboardModal.hide()">
            <span aria-hidden="true">&times;</span>
          </button>
     </div>
     <div class="modal-body marginUnset fuseqGreyBack">
        <div class="fuseqTabset-modal">
          <tabset #tabCreate>
            <tab #tab1 heading="Details">
                <div class="form-group" style="display: flex;">
                    <div class="input-group mr-2">
                        <div class="input-group-prepend" ><span class="input-group-text" style="min-width: 95px;">Name</span></div>
                        <input [disabled]="disabledDashFields == true" type="text" class="form-control" placeholder="Name" [(ngModel)]="dashboardFields.Code">
                    </div>
                    <div class="input-group mr-2">
                        <div class="input-group-prepend" ><span class="input-group-text" style="min-width: 95px;">Description</span></div>
                        <input [disabled]="disabledDashFields == true" type="text" class="form-control" placeholder="Description" [(ngModel)]="dashboardFields.Description" >
                    </div>
                </div>
                <div class="form-group" style="display: flex;">
                    <!--<div class="input-group mr-2">
                        <div class="input-group-prepend" ><span class="input-group-text" style="min-width: 95px;">Active</span></div>
                        <label class="switch switch-label switch-outline-success-alt ml-3"><input [disabled]="disabledDashFields == true" checked=""  class="switch-input" type="checkbox" [(ngModel)]="dashboardFields.Active"><span class="switch-slider" data-checked="✓" data-unchecked="✕" ></span></label>
                    </div>-->
                    <div class="input-group mr-2">
                        <div class="input-group-prepend" ><span class="input-group-text" style="min-width: 95px;">Add To Favorite</span></div>
                        <label class="switch switch-label switch-outline-success-alt ml-3"><input [disabled]="disabledDashFields == true" checked=""  class="switch-input" type="checkbox" [(ngModel)]="dashboardFields.AddToFavorite"><span class="switch-slider" data-checked="✓" data-unchecked="✕" ></span></label>
                    </div>
                </div>
            </tab>
          </tabset>
        </div>
        <div class="modal-footer">

            <div class="dropdown dropdownEvidence" dropdown>
                <button [ngClass]="dialogClass==='pdialogCreate' ? 'btn btn-success' : 'btn btn-warning'" type="button" dropdownToggle (click)="false">
                    Save
                </button>
                <div [ngClass]="dialogClass==='pdialogCreate' ? 'dropdown-menu dropdown-menu-left-success dropleftEvidence' : 'dropdown-menu dropdown-menu-left-warning dropleftEvidence'" aria-labelledby="link-dropdown" *dropdownMenu>
                    <button type="button" [ngClass]="dialogClass==='pdialogCreate' ? 'dropdown-item dropdownBtnSuccess' :'dropdown-item dropdownBtnWarning'" title="Save" (click)="saveDashboardDetails('save')">Save</button>
                    <button type="button" [ngClass]="dialogClass==='pdialogCreate' ? 'dropdown-item dropdownBtnSuccess' :'dropdown-item dropdownBtnWarning'" title="Save & Close" (click)="saveDashboardDetails('saveClose')">Save and Close</button>
                </div>
            </div>

            <!-- <button [hidden]="dashboardType=='New'" class="btn btn-primary" (click)="saveDashboardDetails('save')">Save</button> -->
            <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="this.addDashboardModal.hide()">Cancel</button>
        </div>
    </div>
  </div>
 </div>
</div>






<!-----------------------------------MODAL DETAILS CONTRACT INDICATOR TRACKING PERIOD----------------------------------------->


<div id="detailsModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #detailsModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-xl" style="height: 90%; max-width : 1400px">
<div class="modal-content">
  <div [ngClass]="typeHeader3 ==='compliant' ? 'modal-header modalHeaderCreate' : typeHeader3 ==='noncompliant' ? 'modal-header modalHeaderDanger' : typeHeader3 ==='inactive' ? 'modal-header modalHeaderInactive' : (typeHeader3 ==='notcalculated' || typeHeader3 ==='pending') ? 'modal-header modalHeaderModify' : 'modal-header modalHeaderPrimary'" >

 <!--  <div [ngClass]="typeHeader3 ==='inactive' ? 'modal-header modalHeaderInactive' : 'modal-header modalHeaderPrimary'">

  <div class="modal-header modalHeaderPrimary">
  <div [ngClass]="typeHeader2 ==='incative' ? 'modal-header modalHeaderInactive' : 'modal-header modalHeaderPrimary'">-->

   <h5 class="modal-title uc" id="approveModalLabel">Details</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hideDetailsModal()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetModify>


      
      <tab #tab heading="Metric" class="table-responsive"  style="padding:unset">
        <p-table id="indicatorsTable"  class="table table-hover evidenceDataTable paramTable" #indicatorsTable  [columns]="colsTableIndicator" [value]="indicatorWidgetData" [scrollable]="true" scrollHeight="60vh"
        sortMode="multiple" [multiSortMeta]="[{field: 'nameIndicator', order: 1}, {field: 'process_name', order: 1}]">
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button class="btn btn-outline-dark ml-2" (click)="showColFilters2 = !showColFilters2"><i *ngIf="showColFilters2" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters2" class=" pi pi-filter"></i></button>
                Count: {{indicatorsTable.filteredValue?indicatorsTable.filteredValue.length:indicatorsTable.totalRecords}}
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                    </div>
                    <input type="search" class="form-control"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" style="width:auto">
                  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-row let-columns >
            <tr class="fuseqTableHeader" style="white-space: nowrap;">
              <th pSortableColumn="nameIndicator">metric<p-sortIcon field="nameIndicator"></p-sortIcon></th>
              <th pSortableColumn="description">description<p-sortIcon field="description"></p-sortIcon></th>
             <th [hidden]="typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant' && typeHeader3 != 'othersnull' && typeHeader3 != 'pending' && typeHeader3 != 'notcalculated' "  pSortableColumn="process_name">process<p-sortIcon field="process_name"></p-sortIcon></th>   
          <th  [hidden]="typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant' && typeHeader3 != 'othersnull'"  pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th>
           
           <!-- <th  pSortableColumn="process_name">process<p-sortIcon field="process_name"></p-sortIcon></th>  
            <th    pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th> -->  
              <th pSortableColumn="target">target<p-sortIcon field="target"></p-sortIcon></th>
              <th [hidden]="typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant'" pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th>
            <th [hidden]="typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant' && typeHeader3 != 'othersnull'" pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>
          <!--    <th  pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th> --> 
          
              <th pSortableColumn="tracking_period">t. period<p-sortIcon field="tracking_period"></p-sortIcon></th>
             
              <th pSortableColumn="valid_from">valid from<p-sortIcon field="valid_from"></p-sortIcon></th>
              <th pSortableColumn="valid_to">valid to<p-sortIcon field="valid_to"></p-sortIcon></th>
              <th style="min-width: 90px;" pSortableColumn="valid">valid<p-sortIcon field="valid"></p-sortIcon></th>
              <th pSortableColumn="active">active<p-sortIcon field="active"></p-sortIcon></th>
            </tr>
            <tr *ngIf="showColFilters2" class="scrollFilterTree" > <!-- [hidden]=" (col.field == 'compliant' &&  typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant')" -->
             <th  *ngFor="let col of columns"  [hidden]=" (col.field == 'partial_period' &&  typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant' && typeHeader3 != 'othersnull') || (col.field == 'compliant' &&  typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant') || (col.field == 'result' &&  typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant' && typeHeader3 != 'othersnull') || (col.field == 'process_name' &&  typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant' && typeHeader3 != 'othersnull' && typeHeader3 != 'pending' && typeHeader3 != 'notcalculated')" style="background-color: white !important">
               <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
               style=" text-align:center"  colpsan= "14"
                placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

                [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
                type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()">
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >

              <td style="text-align:center; min-width: 15rem"  class="truncateModal" [tooltip]= "row.nameIndicator" data-container="body">{{row.nameIndicator}}</td>
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.description"  data-container="body">{{row.description}}</td>
             <td [hidden]="typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant' && typeHeader3 != 'othersnull'  && typeHeader3 != 'pending' && typeHeader3 != 'notcalculated'"  style="text-align:center; min-width: 8rem"  class="truncateModal" [tooltip]= "row.process_name" data-container="body">{{row.process_name}}</td>  
             <td  [hidden]="typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant' && typeHeader3 != 'othersnull'" style="text-align:center"  class="truncateModal" [tooltip]= "row.result"  data-container="body">{{row.result}}</td>
     
  <!--  <td   style="text-align:center"  class="truncateModal" [tooltip]= "row.process_name" data-container="body">{{row.process_name}}</td>         
   <td   style="text-align:center"  class="truncateModal" [tooltip]= "row.result"  data-container="body">{{row.result}}</td>-->  
   <td style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>

              <td [hidden]="typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant'"  style="text-align:center">
                <i *ngIf="row.compliant == true" class="fa fa-check fq-success-color"></i>
                <i *ngIf="row.compliant == false" class="fa fa-times fq-danger-color"></i>
              </td>
            <td  [hidden]="typeHeader3 != 'noncompliant' && typeHeader3 != 'compliant' && typeHeader3 != 'othersnull'" style="text-align:center">
                <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
              </td>
          <!--    <td  style="text-align:center">
                <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
              </td>-->
            <td style="text-align:center" class="truncateModal" [tooltip]= "row.tracking_period" data-container="body" >{{row.tracking_period}}</td>
              <td style="text-align:center" class="truncateModal" [tooltip]= "row.valid_from" data-container="body">{{row.valid_from}}</td>
              <td style="text-align:center" class="truncateModal" [tooltip]= "row.valid_to" data-container="body">{{row.valid_to}}</td>
              <td style="text-align:center;padding: 0.5rem 1rem;" *ngIf="row.valid; then trueBlock else falseBlock"></td>
              <ng-template #trueBlock><td style="text-align:center"><i class="fa fa-check fq-success-color"></i></td> </ng-template>
              <ng-template #falseBlock><td style="text-align:center"><i class="fa fa-times fq-danger-color"></i></td> </ng-template>
              <td style="text-align:center;padding: 0.5rem 1rem;" *ngIf="row.active; then trueBlock else falseBlock"></td>
              <ng-template #trueBlock><td style="text-align:center"><i class="fa fa-check fq-success-color"></i></td> </ng-template>
              <ng-template #falseBlock><td style="text-align:center"><i class="fa fa-times fq-danger-color"></i></td> </ng-template>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="10">No data</td>
        </ng-template>
        </p-table>
        </tab>

     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hideDetailsModal()">Close</button>
 </div>
</div>
</div>
</div>



<!-----------------------------------MODAL CALCULATION STATUS DETAILS CONTRACT INDICATOR TRACKING PERIOD----------------------------------------->



<div id="calculationModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #calculationModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-xl" style="height: 90%; max-width : 1400px">
<div class="modal-content">
 
  <div [ngClass]="(typeHeader2 ==='dayCompliant' || typeHeader2 ==='weekCompliant') ? 'modal-header modalHeaderCreate' : (typeHeader2 ==='dayNotcompliant'  || typeHeader2 ==='weekNotcompliant') ?
  'modal-header modalHeaderDanger' : (typeHeader2 ==='dayNotcalculated' || typeHeader2 ==='weekNotcalculated' || typeHeader2 ==='dayPending' || typeHeader2 ==='weekPending') ?
   'modal-header modalHeaderModify' : 'modal-header modalHeaderPrimary'" >

  


   <h5 class="modal-title uc" id="approveModalLabel">Calculation status</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hidecalculationModal()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetModify>
 
    
       <tab #tab heading="Metric" class="table-responsive"  style="padding:unset">
        <p-table  *ngIf="!groupByConfig.tableGrouped" id="indicatorsTable"  class="table table-hover evidenceDataTable paramTable" #indicatorsTable  [columns]="colsCalculation" 
        [value]="indicatorWidgetData" [scrollable]="true" scrollHeight="60vh"      sortMode="multiple" [multiSortMeta]="[{field: 'name_indicator', order: 1}, {field: 'process_name', order: 1}]">
          <ng-template pTemplate="caption" >
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
             <!--   <div class="btn-group" dropdown #dropdown="bs-dropdown" [autoClose]="true">
                  <select class="form-control" style="width: 100%; height: 2rem;" (ngModelChange)="onChangeModal($event)" [(ngModel)]="selectedGroup">            
                    <option value="nogroup">No group</option>
                    <option value="Indicator">By Metrics</option>                                    
                  </select>
                </div>-->
                <button class="btn btn-outline-dark ml-2" (click)="showColFilters4 = !showColFilters4"><i *ngIf="showColFilters4" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters4" class=" pi pi-filter"></i></button>
                Count: {{indicatorsTable.filteredValue?indicatorsTable.filteredValue.length:indicatorsTable.totalRecords}} 
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                    </div>
                    <input type="search" class="form-control"  pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" style="width:auto">
                  </div>
                </div>
            </div>
          </ng-template> 
          <ng-template pTemplate="header" let-row let-columns >
            <tr class="fuseqTableHeader" style="white-space: nowrap;"> 
              <th pSortableColumn="name_indicator">metric<p-sortIcon field="name_indicator"></p-sortIcon></th>  
              <th pSortableColumn="process_name">process<p-sortIcon field="process_name"></p-sortIcon></th>                      
              <th pSortableColumn="name_customer">contract party<p-sortIcon field="name_customer"></p-sortIcon></th> 
              <th [hidden]="typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'weekIndicatorOthersNull'" pSortableColumn="period_week">week<p-sortIcon field="period_week"></p-sortIcon></th> 
              <th  pSortableColumn="period_day">day<p-sortIcon field="period_day"></p-sortIcon></th>              
              <th pSortableColumn="period_month">month<p-sortIcon field="period_month"></p-sortIcon></th>            
              <th pSortableColumn="period_year">year<p-sortIcon field="period_year"></p-sortIcon></th>  
              <th  [hidden]="typeHeader2 != 'dayNotcompliant' && typeHeader2 != 'dayCompliant' && typeHeader2 != 'calculationday' && typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'daykIndicatorOthersNull' && typeHeader2 != 'weekIndicatorOthersNull'"   pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th>
              <th  pSortableColumn="result">target<p-sortIcon field="result"></p-sortIcon></th>
              <th  [hidden]="typeHeader2 != 'dayNotcompliant' && typeHeader2 != 'dayCompliant' && typeHeader2 != 'calculationday' && typeHeader2 != 'weekNotcompliant'  && typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'daykIndicatorOthersNull' && typeHeader2 != 'weekIndicatorOthersNull'"  pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th> 
              <th  [hidden]="typeHeader2 != 'dayNotcompliant' && typeHeader2 != 'dayCompliant' && typeHeader2 != 'calculationday' && typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'daykIndicatorOthersNull' && typeHeader2 != 'weekIndicatorOthersNull'"  pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>
          
              <th *ngIf="typeHeader2 !=='dayPending' && typeHeader2 !=='weekPending'" pSortableColumn="timestamp">Calc time<p-sortIcon field="timestamp"></p-sortIcon></th> 


            </tr>     
            <tr *ngIf="showColFilters4" class="scrollFilterTree" >
              
              <th  *ngFor="let col of columns"
             [hidden]="(col.field == 'partial_period' &&  (typeHeader2 != 'dayNotcompliant' && typeHeader2 != 'dayCompliant'  && typeHeader2 != 'calculationday' && typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'daykIndicatorOthersNull' && typeHeader2 != 'weekIndicatorOthersNull' )) ||
              (col.field == 'compliant' &&  (typeHeader2 != 'dayNotcompliant' && typeHeader2 != 'dayCompliant'  && typeHeader2 != 'calculationday' && typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'daykIndicatorOthersNull' && typeHeader2 != 'weekIndicatorOthersNull')) ||
              (col.field == 'result' &&  (typeHeader2 != 'dayNotcompliant' && typeHeader2 != 'dayCompliant'  && typeHeader2 != 'calculationday' && typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'daykIndicatorOthersNull' && typeHeader2 != 'weekIndicatorOthersNull')) ||
              (col.field == 'period_week' &&  (typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant' && typeHeader2 != 'weekNotcompliant'  && typeHeader2 != 'weekIndicatorOthersNull'))
              || (col.field == 'timestamp' &&  (typeHeader2 == 'dayPending' || typeHeader2 == 'weekPending'))"  style="background-color: white !important">
                <input style="width:100%"  pInputText *ngIf="col.field != 'showField'" 
               style=" text-align:center"  colpsan= "14"
                placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 'true/false' : '' }}" 

                [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
                type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop();">
              </th>
            </tr>         
          </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >        
              <td style="text-align:center; min-width: 15rem"  class="truncateModal" [tooltip]= "row.name_indicator"  data-container="body">{{row.name_indicator}}</td>   
              <td style="text-align:center; min-width: 8rem"  class="truncateModal" [tooltip]= "row.process_name" data-container="body">{{row.process_name}}</td>                        
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.name_customer" data-container="body">{{row.name_customer}}</td>             
              <td [hidden]="typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'weekIndicatorOthersNull'" style="text-align:center"  class="truncateModal" [tooltip]= "row.period_week" data-container="body">{{row.period_week}}</td>                        
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period_day" data-container="body">{{row.period_day}}</td>
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period_month"  data-container="body">{{row.period_month}}</td>   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period_year"  data-container="body">{{row.period_year}}</td>  
              <td  [hidden]="typeHeader2 != 'dayNotcompliant' && typeHeader2 != 'dayCompliant' && typeHeader2 != 'calculationday' && typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant' && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'daykIndicatorOthersNull' && typeHeader2 != 'weekIndicatorOthersNull'"  style="text-align:center"  class="truncateModal" [tooltip]= "row.result"  data-container="body">{{row.result}}</td>
             <td style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>                 
              <td [hidden]="typeHeader2 != 'dayNotcompliant' && typeHeader2 != 'dayCompliant' && typeHeader2 != 'calculationday' && typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'daykIndicatorOthersNull' && typeHeader2 != 'weekIndicatorOthersNull'"   style="text-align:center">
                <i *ngIf="row.compliant == true" class="fa fa-check fq-success-color"></i>
                <i *ngIf="row.compliant == false" class="fa fa-times fq-danger-color"></i>
              </td>     
              <td [hidden]="typeHeader2 != 'dayNotcompliant' && typeHeader2 != 'dayCompliant' && typeHeader2 != 'calculationday' && typeHeader2 != 'calculationweek' && typeHeader2 != 'weekCompliant'  && typeHeader2 != 'weekNotcompliant' && typeHeader2 != 'daykIndicatorOthersNull' && typeHeader2 != 'weekIndicatorOthersNull'" style="text-align:center">
                <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
              </td>
           
              <td *ngIf="typeHeader2 !=='dayPending' && typeHeader2 !=='weekPending'"  style="text-align:center"  class="truncateModal" [tooltip]= "row.timestamp  | date:'yyyy-MM-dd HH:mm'"  data-container="body">{{row.timestamp | date:'yyyy-MM-dd HH:mm'}}</td>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">       
              <td style="text-align:center" colspan="10">No data</td>                 
        </ng-template>
        </p-table>


        <p-treeTable *ngIf="groupByConfig.tableGrouped" [value]="mainData" #indicatorsTable class=" indicatorsTable;" [columns]="colsTable1" styleClass="p-treetable-sm" [globalFilterFields]="['code','customer_name','contract_name','indicator_name','indicator_value','indicator_target', 'step_name']"
              [scrollable]="true" scrollHeight="60vh"  sortMode="multiple" [multiSortMeta]="[{field: 'showField', order: 1},  {field: 'process_name', order: 1},  {field: 'period_day', order: 1}, {field: 'period_month', order: 1}, {field: 'period_year', order: 1}]"  >
              >            
              <ng-template pTemplate="caption">
                <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
                  <div>
                    <div class="btn-group" dropdown #dropdown="bs-dropdown" [autoClose]="true">
                      <select class="form-control" style="width: 100%; height:2rem;" (ngModelChange)="onChangeModal($event)" [(ngModel)]="selectedGroup">
                        <option value="nogroup">No group</option>
                        <option value="Indicator">By Metrics</option>                                    
                      </select>
                    </div>            
                    <button class="btn btn-outline-dark ml-2" (click)="showColFilters4 = !showColFilters4"><i *ngIf="showColFilters4" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters4" class=" pi pi-filter"></i></button>                    
                    Count: {{countindicator}} 
                  </div>
                  <div class="cold-md-6" style="text-align: right">
                    <div class="form-group" style="margin-bottom:unset">
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                        </div>
                        <input type="search" class="form-control" pInputText size="30" (input)="indicatorsTable.filterGlobal($event.target.value, 'contains'); indicatorsTable.resetScrollTop()" style="width:auto">
                      </div>
                    </div>
                  </div>
                </div>
              </ng-template>
                 <ng-template pTemplate="header" let-columns>
                  <tr class="stickyHeader">
                    <ng-container *ngFor="let col of columns">
                      <th style="text-align:center" *ngIf="col.field != groupByConfig.showField" [ttSortableColumn]="col.field">
                        {{col.header}}
                        <p-treeTableSortIcon [field]="col.field"></p-treeTableSortIcon>
                    </th>                   
                  </ng-container>
                  <tr *ngIf="showColFilters4" class="scrollFilterTree" >
                    <ng-container *ngFor="let col of columns">
                    <th *ngIf="col.field != groupByConfig.showField"  style="background-color: white !important">
                      <input style="width:100%"
                     style=" text-align:center"  colpsan= "11" placeholder="{{col.field == 'compliant'  ? 'true/false' : '' }}"
                      pInputText *ngIf="col.field != 'showField' "
                
                      [value]="indicatorsTable.filters[col.field] ? indicatorsTable.filters[col.field].value : ''"
                      type="search" class="form-control" (input)="indicatorsTable.filter($event.target.value, col.field, 'contains'); indicatorsTable.resetScrollTop()">
                    </th>
                  </ng-container>
                  </tr>                 
                </ng-template>
                  <ng-template pTemplate="body" let-rowNode let-rowData="rowData" let-columns="columns">                    
                  <tr>         
                    <ng-container *ngFor="let col of columns; let i = index">
                      <td style="white-space: nowrap; width: 10%" *ngIf="col.field != groupByConfig.showField" [ngStyle]="i == 0 ? {'white-space': 'nowrap'} : {'text-align':'center'}">              
                        <p-treeTableToggler [rowNode]="rowNode" *ngIf="i == 0"></p-treeTableToggler>
                       
                        <span *ngIf="col.field != 'compliant' && col.header !== 'Calc time'  && col.field != 'target' && col.field != 'name_customer'" class="truncateWidget" [tooltip] = "rowData[col.field]"  data-toggle="tooltip" data-placement="left" data-container="body">{{rowData[col.field]}}</span>
                        <span *ngIf="col.field == 'name_customer'" style="min-width: 150px;"  [tooltip]= "rowData.name_customer" class="truncateWidget" data-container="body" >{{rowData.name_customer}}</span>
                     
                        <span *ngIf="col.header == 'Calc time'" class="truncateWidget" [tooltip] = "rowData[col.field] | date:'yyyy-MM-dd HH:mm'" data-container="body" >{{rowData[col.field] | date:'yyyy-MM-dd HH:mm'}}</span>
                        <span *ngIf="col.field == 'compliant'"><i *ngIf="rowData[col.field] == true" class="fa fa-check fq-success-color"></i><i *ngIf="rowData[col.field] == false" class="fa fa-times fq-danger-color"></i></span>                         
                        <span *ngIf="col.field == 'target'" [tooltip]= "rowData.comparison_operator + ' ' + rowData.target + ' ' + rowData.unitMeasureSymbol" class="truncateWidget" data-container="body" >{{rowData.comparison_operator}} {{rowData.target}} {{rowData.unitMeasureSymbol}}</span>
                      </td>
                </ng-container>
                  </tr>
              </ng-template>
              <ng-template pTemplate="emptymessage">
                <tr>
                    <td style="text-align:center" [attr.colspan]="10">
                      No data to display
                    </td>
                </tr>
            </ng-template>
              </p-treeTable>
        </tab>     
     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hidecalculationModal()">Close</button>
 </div>
</div>
</div>
</div>




<!-----------------------------------MODAL DETAILS CONTRACT INDICATOR COMPLIANT----------------------------------------->


<div id="detailsCompliantModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #detailsCompliantModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog modal-lg">
<div class="modal-content">

  <div [ngClass]="typeHeader ==='compliant' ? 'modal-header modalHeaderCreate' : 'modal-header modalHeaderDanger'">
   <h5 class="modal-title uc" id="approveModalLabel">Details</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hideDetailsCompliantModal()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetModify>
       <tab heading="Details" style="padding:unset">

       </tab>

     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hideDetailsCompliantModal()">Close</button>
 </div>
</div>
</div>
</div>



<!-----------------------------------MODAL DESCRIPTION WIDGET----------------------------------------->


<div id="descriptionModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #descriptionModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog modal-lg">
  <div class="modal-content">
    <div class="modal-header modalHeaderPrimary">
   <h5 class="modal-title uc" id="approveModalLabel">Description</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hideDescriptionModal()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div  class="fuseqTabset-modal">
      <!-- <div class="box-body2" style="background-color: #ffffff;">
      {{descriptionWidgetContract}}
        </div>-->
        <div class="input-group">
          <textarea disabled rows="15"  style="background-color: #ffffff;" class="form-control" pInputTextarea [(ngModel)]="descriptionWidgetContract"></textarea>
          </div> 
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hideDescriptionModal()">Close</button>
 </div>
</div>
</div>
</div>


<!-----------------------------------MODAL FILTER WIDGET----------------------------------------->


<div id="filterModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #filterModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 20000">
<div class="modal-dialog modal-md">
<div class="modal-content">
 <div class="modal-header modalHeaderPrimary">
   <h5 class="modal-title uc" id="approveModalLabel">Date filter</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hidefilterModal()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div  class="fuseqTabset-modal">
    <tabset #tabsetContarct>
      <tab  *ngIf="contractWidgetData != null" heading="Widget contract" style="text-align: center">
    <div class="cold-md-6" style="display: flex">

   <!--   <div class="btn-group" dropdown #dropdown="bs-dropdown" [autoClose]="true">
        <select class="form-control" style="width: 100%; height: 2.1rem;" (ngModelChange)="onChangeWidget($event)" [(ngModel)]="selectedGroup">
          <option [hidden] = "this.contractWidgetData == null" value="Contract">Contract</option>
        <option  [hidden] = "this.contractWidgetData != null"  value="nogroup">No filter</option>
        </select>
      </div>-->



      <div class="cold-md-6" style="display: flex">
        <div   class="form-group" style="display: flex;">
          <div  class="row">
             <div class="col-sm">
         <div class="input-group" style="width: 120%">
          <!--  <div class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Month</span></div>
           [hidden]="FilterWidgetContract"-->
            <select [(ngModel)]="monthSelected" class="form-control ms-3" style="width:15%;">
              <!--<option value="">{{'months.all' | translate}}</option>-->
              <option value="1">{{'months.january' | translate}}</option>
              <option value="2">{{'months.february' | translate}}</option>
              <option value="3">{{'months.march' | translate}}</option>
              <option value="4">{{'months.april' | translate}}</option>
              <option value="5">{{'months.may' | translate}}</option>
              <option value="6">{{'months.june' | translate}}</option>
              <option value="7">{{'months.july' | translate}}</option>
              <option value="8">{{'months.august' | translate}}</option>
              <option value="9">{{'months.september' | translate}}</option>
              <option value="10">{{'months.october' | translate}}</option>
              <option value="11">{{'months.november' | translate}}</option>
              <option value="12">{{'months.december' | translate}}</option>
            </select>
          </div></div>
          <div class="col-sm">
           <div class="input-group mr-1">
           <!--  <div class="input-group-prepend"><span class="input-group-text" style="min-width: 95px;">Year</span></div>-->
             <select [(ngModel)]="yearSelected" class="form-control ms-1" style="width:15%;">
              <option value="">Year</option>
              <option *ngFor="let year of anni">{{year}}</option>
            </select>
           </div></div>
           <button style="font-size:10px; width: 35px;" class="btn btn-outline-warning btn-sm" (click)= "refreshJobList()"><i class="fa fa-refresh"></i></button>
          </div>
        </div>

      </div>
    </div>
  </tab>
</tabset>
   </div>
 </div>

 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hidefilterModal()">Close</button>
 </div>
</div>
</div>
</div>












<!-----------------------------------MODAL RECORD ANDAMENTO CONTRATTO ----------------------------------------->


<div id="recordModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #recordModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-xl" style="height: 90%; max-width : 1000px">
<div class="modal-content">
  <div class="modal-header modalHeaderPrimary">

   <h5 class="modal-title uc" id="approveModalLabel">Details</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hiderecordModal()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetWidget>


      <tab #tab heading="Metric" class="table-responsive"  style="padding:unset">
        <p-table id="indicatorsWidgetTable"  class="table table-hover evidenceDataTable paramTable" #indicatorsWidgetTable  [columns]="colsTableRecord" [value]="recordTable" [scrollable]="true" scrollHeight="60vh"
        (sortFunction)="customSort($event)" [customSort]="true">
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button class="btn btn-outline-dark ml-2" (click)="showColFilters2 = !showColFilters2"><i *ngIf="showColFilters2" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters2" class=" pi pi-filter"></i></button>
                Count: {{indicatorsWidgetTable.filteredValue?indicatorsWidgetTable.filteredValue.length:indicatorsWidgetTable.totalRecords}} 
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                    </div>
                    <input type="search" class="form-control" [(ngModel)]="inputSearch"  pInputText size="30" (input)="indicatorsWidgetTable.filterGlobal($event.target.value, 'contains'); indicatorsWidgetTable.resetScrollTop()" style="width:auto; display: unset;">    
                  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-row let-columns >
            <tr class="fuseqTableHeader" style="white-space: nowrap;">
              <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
              <th *ngIf="hiddenTarget == true" pSortableColumn="target">target<p-sortIcon field="target"></p-sortIcon></th> 
              <th pSortableColumn="result">result<p-sortIcon field="result"></p-sortIcon></th>  
              <th *ngIf="hiddenTarget  == true" pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th>        
              <th pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th> 
              <th>actions</th>                 
            </tr>
            <tr *ngIf="showColFilters2" class="scrollFilterTree" >
              <th  *ngFor="let col of columns" style="background-color: white !important" [hidden]="(hiddenTarget == false && (col.field == 'target' || col.field == 'compliant'))" > 
               <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
               style=" text-align:center"  colpsan= "14"
                placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
               
                [value]="indicatorsWidgetTable.filters[col.field] ? indicatorsWidgetTable.filters[col.field].value : ''"
                type="search" class="form-control" (input)="indicatorsWidgetTable.filter($event.target.value, col.field, 'contains'); indicatorsWidgetTable.resetScrollTop()">
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>     
              <td *ngIf="hiddenTarget == true" style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>
              <td *ngIf="row.result != ''"   style="text-align:center" class="truncateModal"  [tooltip]= "row.result + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.result}} {{row.unitMeasureSymbol}}</td>  
              <td *ngIf="row.result == '' " style="text-align:center">{{ ' ' }}</td>  
              <td *ngIf="hiddenTarget  == true" style="text-align:center">
                <i *ngIf="row.compliant == true" class="fa fa-check fq-success-color"></i>
                <i *ngIf="row.compliant == false" class="fa fa-times fq-danger-color"></i>
              </td>
              <td style="text-align:center">
                <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
              </td>    
              <td style="text-align:center">
                <button *ngIf="row.partial_period !== null" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                    <i class="fa-solid fa-folder-open"></i>
                  </button>
                  <button *ngIf="row.partial_period === null" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                    <i class="fa-solid fa-folder-open"></i>
                  </button>
                </td>
           <!--  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.result" data-container="body">{{row.result}}</td>   --> 
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="10">No data</td>
        </ng-template>
        </p-table>
        </tab>
        <tab #tab heading="Threshold" class="table-responsive"  style="padding:unset">
          <p-table id="thTable"  class="table table-hover evidenceDataTable paramTable" #thTable  [columns]="colsTableTh" [value]="thresholdTable" [scrollable]="true" scrollHeight="60vh"
          >
             <ng-template pTemplate="caption">
               <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
                 <div>
                   <button class="btn btn-outline-dark ml-2" (click)="showColFilters15 = !showColFilters15"><i *ngIf="showColFilters15" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters15" class=" pi pi-filter"></i></button>
                
                   Count: {{thTable.filteredValue?thTable.filteredValue.length:thTable.totalRecords}} 
                 </div>
                   <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                     <div class="input-group">
                       <div class="input-group-prepend">
                         <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                       </div>
                       <input type="search" class="form-control" [(ngModel)]="inputSearch2"  pInputText size="30" (input)="thTable.filterGlobal($event.target.value, 'contains'); thTable.resetScrollTop()" style="width:auto">
                     </div>
                   </div>
               </div>
             </ng-template>
             <ng-template pTemplate="header" let-row let-columns >
               <tr class="fuseqTableHeader" style="white-space: nowrap;">
                 <th pSortableColumn="name">Threshold<p-sortIcon field="name"></p-sortIcon></th>  
                 <th pSortableColumn="comparison_operator">Comparison operator<p-sortIcon field="comparison_operator"></p-sortIcon></th> 
                 <th pSortableColumn="value">Value<p-sortIcon field="value"></p-sortIcon></th>       
                 <th pSortableColumn="color">Color<p-sortIcon field="color"></p-sortIcon></th>                                           
               </tr>
               <tr *ngIf="showColFilters15" class="scrollFilterTree" >
                <th  *ngFor="let col of columns" style="background-color: white !important">
                  <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
                  style=" text-align:center"  colpsan= "14"
                   placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
                  
                   [value]="thTable.filters[col.field] ? thTable.filters[col.field].value : ''"
                   type="search" class="form-control" (input)="thTable.filter($event.target.value, col.field, 'contains'); thTable.resetScrollTop()">
                 </th>
               </tr>
             </ng-template>
             <ng-template pTemplate="body" let-row let-columns="columns">
               <tr >
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.name" data-container="body">{{row.name}}</td>   
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.comparison_operator" data-container="body">{{row.comparison_operator}}</td>   
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.value" data-container="body">{{row.value}}</td>   
                <td style="text-align:center"  class="truncateModal" [tooltip]= "row.color" data-container="body">
                  <i class="fa fa-circle" style="font-size: 24px;" [ngStyle]="row.color ? {'color': row.color} : ''"></i></td>   
              <!--  <ng-container *ngFor="let col of columns">
                <span *ngIf="col.field != 'color'" class="truncateModal" [tooltip] = "row[col.field]" container="body" >{{row[col.field]}}</span>
                <span style="text-align:center" *ngIf="col.field == 'color'"><i class="fa fa-circle" style="font-size: 24px;" [ngStyle]="row[col.field] ? {'color': row[col.field]} : ''"></i></span>
              </ng-container>-->
            </tr>
              </ng-template>
             <ng-template pTemplate="emptymessage">
                 <td style="text-align:center" colspan="10">No data</td>
           </ng-template>
           </p-table>
        </tab>
     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hiderecordModal()">Close</button>
 </div>
</div>
</div>
</div>

<!-----------------------------------MODAL RECORD TREND CONTRATTO ----------------------------------------->


<div id="recordContractModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #recordContractModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-lg">
<div class="modal-content">
  <div class="modal-header modalHeaderPrimary">
   <h5 class="modal-title uc" id="approveModalLabel">Details</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hiderecordContractModal()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetWidget>


       <tab #tab heading="Contract Trend" class="table-responsive"  style="padding:unset">
        <p-table id="contractWidgetTable"  class="table table-hover evidenceDataTable paramTable" #contractWidgetTable  [columns]="colsTableContract" [value]="recordContractTable" [scrollable]="true" scrollHeight="60vh" sortMode="multiple"
       >
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button class="btn btn-outline-dark ml-2" (click)="showColFilters22 = !showColFilters22"><i *ngIf="showColFilters22" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters22" class=" pi pi-filter"></i></button>
                Count: {{contractWidgetTable.filteredValue?contractWidgetTable.filteredValue.length:contractWidgetTable.totalRecords}} 
               
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                    </div>
                    <input type="search" class="form-control" [(ngModel)]="inputSearchContract"  pInputText size="30" (input)="contractWidgetTable.filterGlobal($event.target.value, 'contains'); contractWidgetTable.resetScrollTop()" style="width:auto">
                  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-row let-columns >
            <tr class="fuseqTableHeader" style="white-space: nowrap;">
              <th pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
              <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliant"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
              <th pSortableColumn="serieNotCalculated">Others<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>    
               <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>   
              <th pSortableColumn="serienotcalculatedTotal">Not calculated <p-sortIcon field="serienotcalculatedTotal"></p-sortIcon></th>                      
             
             
            </tr>
            <tr *ngIf="showColFilters22" class="scrollFilterTree" >
             <th  *ngFor="let col of columns" style="background-color: white !important">
               <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
               style=" text-align:center"  colpsan= "14"
                placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

                [value]="contractWidgetTable.filters[col.field] ? contractWidgetTable.filters[col.field].value : ''"
                type="search" class="form-control" (input)="contractWidgetTable.filter($event.target.value, col.field, 'contains'); contractWidgetTable.resetScrollTop()">
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>                   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>  
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serienotcalculatedTotal" data-container="body">{{row.serienotcalculatedTotal}}</td>   
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="4">No data</td>
        </ng-template>
        </p-table>
        </tab>
     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hiderecordContractModal()">Close</button>
 </div>
</div>
</div>
</div>


<!-- End trend contract-->


<!-----------------------------------MODAL RECORD TREND recordCustomerModal ----------------------------------------->


<div id="recordCustomerModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #recordCustomerModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-xl" style="height: 90%; max-width : 1200px">
<div class="modal-content">
  <div class="modal-header modalHeaderPrimary">

   <h5 class="modal-title uc" id="approveModalLabel">Details</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hiderecordCustomerModal()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetWidget>


       <tab #tab heading="Contract Party Trend" class="table-responsive"  style="padding:unset">
        <p-table id="customerTable"  class="table table-hover evidenceDataTable paramTable" #customerTable  [columns]="colsTableCustomer" [value]="recordCustomerTable" [scrollable]="true" scrollHeight="60vh"  [sortMode]="'multiple'" 
       >
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button class="btn btn-outline-dark ml-2" (click)="showColFilters23 = !showColFilters23"><i *ngIf="showColFilters23" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters23" class=" pi pi-filter"></i></button>
                Count: {{customerTable.filteredValue?customerTable.filteredValue.length:customerTable.totalRecords}} 
                
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                    </div>
                    <input type="search" class="form-control"  pInputText size="30" (input)="customerTable.filterGlobal($event.target.value, 'contains'); customerTable.resetScrollTop()" style="width:auto">
                  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-row let-columns >
            <tr class="fuseqTableHeader" style="white-space: nowrap;">
              <th pSortableColumn="nameYaxis">Contract<p-sortIcon field="nameYaxis"></p-sortIcon></th>  
              <th pSortableColumn="serieCompliant">Compliant<p-sortIcon field="serieCompliant"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCompliant">Not compliant<p-sortIcon field="serieNotCompliant"></p-sortIcon></th>        
              <th pSortableColumn="serieNotCalculated">Not calculated<p-sortIcon field="serieNotCalculated"></p-sortIcon></th>                         
              <th pSortableColumn="serieOthers">Others<p-sortIcon field="serieOthers"></p-sortIcon></th>        
              <th pSortableColumn="seriePending">Pending<p-sortIcon field="seriePending"></p-sortIcon></th>   
             
            </tr>
            <tr *ngIf="showColFilters23" class="scrollFilterTree" >
             <th  *ngFor="let col of columns" style="background-color: white !important">
               <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
               style=" text-align:center"  colpsan= "14"
                placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

                [value]="customerTable.filters[col.field] ? customerTable.filters[col.field].value : ''"
                type="search" class="form-control" (input)="customerTable.filter($event.target.value, col.field, 'contains'); customerTable.resetScrollTop()">
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >
              <td style="text-align:center"  class="truncateWidget" [tooltip]= "row.nameYaxis"  data-container="body">{{row.nameYaxis}}</td>                   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliant" data-container="body">{{row.serieCompliant}}</td>   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliant" data-container="body">{{row.serieNotCompliant}}</td>   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculated" data-container="body">{{row.serieNotCalculated}}</td>  
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieOthers" data-container="body">{{row.serieOthers}}</td>   
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriePending" data-container="body">{{row.seriePending}}</td>   
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="4">No data</td>
        </ng-template>
        </p-table>
        </tab>
     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hiderecordCustomerModal()">Close</button>
 </div>
</div>
</div>
</div>

<!-- END recordCustomerModal-->
<!-- Table  ALL-->



<div id="recordContractAll" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #recordContractAll="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-xl">
<div class="modal-content">
  <div class="modal-header modalHeaderPrimary">

   <h5 class="modal-title uc" id="approveModalLabel">Details</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hideContractAll()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetWidget>


       <tab #tab heading="Contract Rate" class="table-responsive"  style="padding:unset">
        <p-table id="contractTableAll"  class="table table-hover evidenceDataTable paramTable" #contractTableAll  [columns]="colsTableAll" [value]="recordContractTable" [scrollable]="true" scrollHeight="60vh"  sortMode="multiple"
       >
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button class="btn btn-outline-dark ml-2" (click)="showColFiltersAll = !showColFiltersAll"><i *ngIf="showColFiltersAll" class=" pi pi-filter-slash"></i><i *ngIf="!showColFiltersAll" class=" pi pi-filter"></i></button>
           <!--  Count: {{contractTableAll.filteredValue?contractTableAll.filteredValue.length:contractTableAll.totalRecords}} -->   
                
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                    </div>
                    <input type="search" class="form-control"  pInputText size="30" (input)="contractTableAll.filterGlobal($event.target.value, 'contains'); contractTableAll.resetScrollTop()" style="width:auto">
                  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-row let-columns >
            <tr class="fuseqTableHeader" style="white-space: nowrap;">
          <!--   <th pSortableColumn="serieCompliantDay">Compliant Day <p-sortIcon field="serieCompliantDay"></p-sortIcon></th> 
              <th pSortableColumn="serieCompliantWeek">Compliant Week <p-sortIcon field="serieCompliantWeek"></p-sortIcon></th> 
              <th pSortableColumn="serieCompliantMonth">Compliant Month<p-sortIcon field="serieCompliantMonth"></p-sortIcon></th> 
              <th pSortableColumn="serieCompliantQuarter">Compliant Quarter<p-sortIcon field="serieCompliantQuarter"></p-sortIcon></th>        
              <th pSortableColumn="serieCompliantHalfYear">Compliant Half-year<p-sortIcon field="serieCompliantHalfYear"></p-sortIcon></th>      
              <th pSortableColumn="serieCompliantYear">Compliant Year<p-sortIcon field="serieCompliantYear"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCompliantDay">Not Compliant Day <p-sortIcon field="serieNotCompliantDay"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCompliantWeek">Not Compliant Week <p-sortIcon field="serieNotCompliantWeek"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCompliantMonth">Not Compliant Month<p-sortIcon field="serieNotCompliantMonth"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCompliantQuarter">Not Compliant Quarter<p-sortIcon field="serieNotCompliantQuarter"></p-sortIcon></th>        
              <th pSortableColumn="serieNotCompliantHalfYear">Not Compliant Half-year<p-sortIcon field="serieNotCompliantHalfYear"></p-sortIcon></th>      
              <th pSortableColumn="serieNotCompliantYear">Not Compliant Year<p-sortIcon field="serieNotCompliantYear"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCalculatedDay">Not Calculated Day <p-sortIcon field="serieNotCalculatedDay"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCalculatedWeek">Not Calculated Week <p-sortIcon field="serieNotCalculatedWeek"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCalculatedMonth">Not Calculated Month<p-sortIcon field="serieNotCalculatedMonth"></p-sortIcon></th> 
              <th pSortableColumn="serieNotCalculatedQuarter">Not Calculated Quarter<p-sortIcon field="serieNotCalculatedQuarter"></p-sortIcon></th>        
              <th pSortableColumn="serieNotCalculatedHalfYear">Not Calculated Half-year<p-sortIcon field="serieNotCalculatedHalfYear"></p-sortIcon></th>      
              <th pSortableColumn="serieNotCalculatedYear">Not Calculated Year<p-sortIcon field="serieNotCalculatedYear"></p-sortIcon></th> 
              <th pSortableColumn="serieOthersDay">Others Day <p-sortIcon field="serieOthersDay"></p-sortIcon></th> 
              <th pSortableColumn="serieOthersWeek">Others Week <p-sortIcon field="serieOthersWeek"></p-sortIcon></th> 
              <th pSortableColumn="serieOthersMonth">Others Month<p-sortIcon field="serieOthersMonth"></p-sortIcon></th> 
              <th pSortableColumn="serieOthersQuarter">Others Quarter<p-sortIcon field="serieOthersQuarter"></p-sortIcon></th>        
              <th pSortableColumn="serieOthersHalfYear">Others Half-year<p-sortIcon field="serieOthersHalfYear"></p-sortIcon></th>      
              <th pSortableColumn="serieOthersYear">Others Year<p-sortIcon field="serieOthersYear"></p-sortIcon></th>  -->
              <th pSortableColumn="serieName">calculation Summary <p-sortIcon field="serieName"></p-sortIcon></th>
              <th pSortableColumn="value">Value <p-sortIcon field="value"></p-sortIcon></th>
                   
           
            </tr>
            <tr *ngIf="showColFiltersAll" class="scrollFilterTree" >
             <th  *ngFor="let col of columns" style="background-color: white !important">
              <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
               style=" text-align:center"  colpsan= "14"
                placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
          
                [value]="contractTableAll.filters[col.field] ? contractTableAll.filters[col.field].value : ''"
                type="search" class="form-control" (input)="contractTableAll.filter($event.target.value, col.field, 'contains'); contractTableAll.resetScrollTop()"></div>
              </th>
            </tr>
          </ng-template>
              <ng-template pTemplate="body" let-row  let-i="index">
                <tr >
                  <td style="text-align:center"  class="truncateModal"  [tooltip]= "row.serieName">{{ row.serieName }}</td>
                  <td style="text-align:center"  class="truncateModal"  [tooltip]= "row.value + '%'"  >{{ row.value }}{{'%'}}</td>
          
               <!--   <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantDay + '%'" data-container="body">{{row.serieCompliantDay}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantMonth + '%'" data-container="body">{{row.serieCompliantMonth}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantQuarter + '%'" data-container="body">{{row.serieCompliantQuarter}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantHalfYear + '%'" data-container="body">{{row.serieCompliantHalfYear}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieCompliantYear + '%'" data-container="body">{{row.serieCompliantYear}}</td> 
          
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantDay + '%'" data-container="body">{{row.serieNotCompliantDay}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantMonth + '%'" data-container="body">{{row.serieNotCompliantMonth}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantQuarter + '%'" data-container="body">{{row.serieNotCompliantQuarter}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantHalfYear + '%'" data-container="body">{{row.serieNotCompliantHalfYear}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCompliantYear + '%'" data-container="body">{{row.serieNotCompliantYear}}{{'%'}}</td> 
          
                  
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedDay + '%'" data-container="body">{{row.serieNotCalculatedDay}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedMonth + '%'" data-container="body">{{row.serieNotCalculatedMonth}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedQuarter + '%'" data-container="body">{{row.serieNotCalculatedQuarter}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedHalfYear + '%'" data-container="body">{{row.serieNotCalculatedHalfYear}}{{'%'}}</td>   
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.serieNotCalculatedYear + '%'" data-container="body">{{row.serieNotCalculatedYear}}{{'%'}}</td> 
           -->     </tr>
              </ng-template>
          <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="4">No data</td>
        </ng-template>
        </p-table>
        </tab>
     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hideContractAll()">Close</button>
 </div>
</div>
</div>
</div>


<!--EnD Table  recordTablePenalty-->













<!-- Table  recordTablePenalty-->



<div id="recordTablePenalty" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #recordTablePenalty="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-xl" style="height: 90%; max-width : 1000px">
<div class="modal-content">
  <div class="modal-header modalHeaderPrimary">

   <h5 class="modal-title uc" id="approveModalLabel">Details</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hiderecordTablePenalty()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetWidget>


       <tab #tab heading="Penalty Trend" class="table-responsive"  style="padding:unset">
        <p-table id="penaltyTable"  class="table table-hover evidenceDataTable paramTable" #penaltyTable  [columns]="colsTablePenalty" [value]="tablePenalty" [scrollable]="true" scrollHeight="60vh"  [sortMode]="'multiple'" 
       >
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button class="btn btn-outline-dark ml-2" (click)="showColFilters25 = !showColFilters25"><i *ngIf="showColFilters25" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters25" class=" pi pi-filter"></i></button>
                Count: {{penaltyTable.filteredValue?penaltyTable.filteredValue.length:penaltyTable.totalRecords}} 
                
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                    </div>
                    <input type="search" class="form-control" [(ngModel)]="inputSearch3"  pInputText size="30" (input)="penaltyTable.filterGlobal($event.target.value, 'contains'); penaltyTable.resetScrollTop()" style="width:auto">
                  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-row let-columns >
            <tr class="fuseqTableHeader" style="white-space: nowrap;">
              <th pSortableColumn="period">Period<p-sortIcon field="period"></p-sortIcon></th>  
              <th pSortableColumn="seriePenalty">Penalty<p-sortIcon field="seriePenalty"></p-sortIcon></th>  
              <th pSortableColumn="partial_period">Complete<p-sortIcon field="partial_period"></p-sortIcon></th>      
              <th>actions</th>          
             
             
            </tr>
            <tr *ngIf="showColFilters25" class="scrollFilterTree" >
             <th  *ngFor="let col of columns" style="background-color: white !important">
               <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
               style=" text-align:center"  colpsan= "14"
                placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'valid'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"

                [value]="penaltyTable.filters[col.field] ? penaltyTable.filters[col.field].value : ''"
                type="search" class="form-control" (input)="penaltyTable.filter($event.target.value, col.field, 'contains'); penaltyTable.resetScrollTop()">
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-row  let-i="index">
            <tr >
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>                   
              <td *ngIf="row.seriePenalty != ''"   style="text-align:center" class="truncateModal"  [tooltip]= "row.seriePenalty + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.seriePenalty}} {{row.unitMeasureSymbol}}</td>  
              <td *ngIf="row.seriePenalty == '' " style="text-align:center" class="truncateModal" [tooltip]= "''" data-container="body">{{ '' }}</td>  
              <td  *ngIf="row.seriePenalty != ''"  style="text-align:center">
                <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
              </td>
              <td  *ngIf="row.seriePenalty == ''"  style="text-align:center">{{ '' }}

              </td>
              <td style="text-align:center">
                <button *ngIf="row.partial_period !== ''" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'rawData')">
                      <i class="fa-solid fa-folder-open"></i>
                    </button>
                    <button *ngIf="row.partial_period === ''" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                      <i class="fa-solid fa-folder-open"></i>
                    </button>
                  </td>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="4">No data</td>
        </ng-template>
        </p-table>
        </tab>
     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hiderecordTablePenalty()">Close</button>
 </div>
</div>
</div>
</div>


<!--EnD Table  recordTablePenalty-->


<!-- Survey Modal -->
<div id="detailsModalSurvey" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #detailsModalSurvey="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-xl" style="height: 90%; max-width : 1200px">
<div class="modal-content">
  <div class="modal-header modalHeaderPrimary">
    <h5 *ngIf="typeHeaderSurvey == 'contract_completed'" class="modal-title" id="createModalLabel">Contracts section: Completed </h5>
    <h5 *ngIf="typeHeaderSurvey == 'contract_onGoing'" class="modal-title" id="createModalLabel">Contracts section: On going/Scheduled</h5>
    <h5 *ngIf="typeHeaderSurvey == 'contract_threshold'" class="modal-title" id="createModalLabel">Contracts section: Below Percentage</h5>
    <h5 *ngIf="typeHeaderSurvey == 'contract_average'" class="modal-title" id="createModalLabel">Contracts section: Below Average</h5>
    <h5 *ngIf="typeHeaderSurvey == 'compaign_completed'" class="modal-title" id="createModalLabel">Campaigns section : Completed </h5>
    <h5 *ngIf="typeHeaderSurvey == 'compaign_start'" class="modal-title" id="createModalLabel">Campaigns section: On going/Scheduled</h5>
    <h5 *ngIf="typeHeaderSurvey == 'compaign_threshold'" class="modal-title" id="createModalLabel">Campaigns section: Below Percentage</h5>
    <h5 *ngIf="typeHeaderSurvey == 'compaign_average'" class="modal-title" id="createModalLabel">Campaigns section: Below Average</h5>
    <h5 *ngIf="typeHeaderSurvey == 'user_tosend'" class="modal-title" id="createModalLabel">Users section: Not completed </h5>
    <h5 *ngIf="typeHeaderSurvey == 'user_details'" class="modal-title" id="createModalLabel">Users section: Details</h5>


  <!-- <h5 *ngIf="typeHeaderSurvey == 'MeasureMaxMin'" class="modal-title" id="createModalLabel">Measure Details </h5>
    <h5 *ngIf="typeHeaderSurvey == 'MeasureAverage'" class="modal-title" id="createModalLabel">Measure Average</h5>
    <h5 *ngIf="typeHeaderSurvey == 'MeasureTop'" class="modal-title" id="createModalLabel">Measure Top 5</h5>
    <h5 *ngIf="typeHeaderSurvey == 'MeasureWrost'" class="modal-title" id="createModalLabel">Measure Wrost 5</h5>--> 
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hideModelSurvey()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetWidget> 


       <tab #tab heading="Details" class="table-responsive"  style="padding:unset">
        <p-table id="detailsSurvey"  class="table table-hover evidenceDataTable paramTable" #detailsSurvey  [columns]="colsTableSuyrvey" [value]="tableSurvey" [scrollable]="true" scrollHeight="60vh"
        dataKey="contract_name"   [rowGroupMode]="typeHeaderSurvey == 'contract_completed' ? null : 'subheader'" [groupRowsBy]="typeHeaderSurvey == 'contract_completed' ? null : 'contract_name'"   (onTableReady)="expandAllGroups()"
        sortMode="multiple"  sortMode="multiple"  [multiSortMeta]="[{field: 'contract_name', order: 1}, {field: 'campaign_name', order: 1}, {field: 'period', order: 1}]"
          (onFilter)="countUniqueItems()">      >
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button  *ngIf="typeHeaderSurvey != 'contract_completed'" class="btn btn-outline-dark ml-2" (click)="toggleAllGroups()">
                  <ng-container *ngIf="allExpanded; else expandTemplate">
                    <i class="pi pi-chevron-down"  style="vertical-align: bottom;"></i>
                    Collapse All
                  </ng-container>
                  <ng-template #expandTemplate>
                    <i class="pi pi-chevron-right"  style="vertical-align: bottom;"></i>
                    Expand All
                  </ng-template>
                </button>
                
                <button class="btn btn-outline-dark ml-2" (click)="showColFilters26 = !showColFilters26"><i *ngIf="showColFilters26" class=" pi pi-filter-slash"></i><i *ngIf="!showColFilters26" class=" pi pi-filter"></i></button>
                Contracts: {{uniqueContractsCount}}
                <span *ngIf="typeHeaderSurvey == 'compaign_completed' || typeHeaderSurvey == 'compaign_start' || typeHeaderSurvey == 'compaign_threshold'  || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'">
                Periods of Campaigns:{{detailsSurvey.filteredValue?detailsSurvey.filteredValue.length:detailsSurvey.totalRecords}}</span>
               <!-- Count: {{detailsSurvey.filteredValue?detailsSurvey.filteredValue.length:detailsSurvey.totalRecords}}
                  <span *ngIf="typeHeaderSurvey == 'compaign_completed' || typeHeaderSurvey == 'compaign_start' || typeHeaderSurvey == 'compaign_threshold'  || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'">
               Campaigns: {{uniqueCampaignsCount}}</span>
               -->
              </div> 
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                    </div>
                    <input type="search" class="form-control"  [(ngModel)]="inputSearch4" pInputText size="30" (input)="detailsSurvey.filterGlobal($event.target.value, 'contains'); detailsSurvey.resetScrollTop(); countUniqueItems()" style="width:auto">
                  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-columns>
            <tr class="fuseqTableHeader" style="white-space: nowrap;">
              <th pSortableColumn="contract_name">Contract
                <p-sortIcon field="contract_name"></p-sortIcon>
              </th>
            
              <th *ngIf="typeHeaderSurvey == 'compaign_completed' || typeHeaderSurvey == 'compaign_start'  || typeHeaderSurvey == 'compaign_threshold'|| typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details' || typeHeaderSurvey == 'compaign_average' " 
                  pSortableColumn="campaign_name">Campaign
                <p-sortIcon field="campaign_name"></p-sortIcon>
              </th>         
              <th *ngIf="typeHeaderSurvey != 'contract_threshold'  && typeHeaderSurvey != 'user_details' &&  typeHeaderSurvey != 'contract_average'" 
                  pSortableColumn="period">Start date
                <p-sortIcon field="period"></p-sortIcon>
              </th>        
              <!-- <th *ngIf="typeHeaderSurvey == 'contract_threshold'" pSortableColumn="below_threshold_campaigns">Below threshold campaigns
                <p-sortIcon field="below_threshold_campaigns"></p-sortIcon>
              </th> -->      
              <th *ngIf="typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'contract_average'  || typeHeaderSurvey == 'contract_threshold'" 
                  pSortableColumn="{{ (typeHeaderSurvey == 'contract_threshold') ? 'below_threshold_campaigns' : 'row.campaign_count' }}">Number of Campaigns
                <p-sortIcon field="{{ (typeHeaderSurvey == 'contract_threshold') ? 'below_threshold_campaigns' : 'row.campaign_count' }}"></p-sortIcon>
              </th>        
              <th *ngIf="typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'contract_average' ||  typeHeaderSurvey == 'compaign_average'"
                  pSortableColumn="{{ (typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold') ? 'calculation_result' : 'average_result' }}">
                Result
                <p-sortIcon field="{{ (typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold') ? 'calculation_result' : 'average_result' }}"></p-sortIcon>
              </th>         
              <th *ngIf="typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold'   || typeHeaderSurvey == 'compaign_average'  || typeHeaderSurvey == 'contract_average'" 
                  pSortableColumn="target">Target
                <p-sortIcon field="target"></p-sortIcon>
              </th>
            
              <th *ngIf="typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'compaign_completed' || typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_average'  || typeHeaderSurvey == 'compaign_average' "
                  pSortableColumn="{{ (typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'compaign_threshold') ? 'row.user_participation_count' : 'row.total_users' }}">
                Total Users
                <p-sortIcon field="{{ (typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'compaign_threshold') ? 'row.user_participation_count' : 'row.total_users' }}"></p-sortIcon>
              </th>         
              <th *ngIf="typeHeaderSurvey == 'compaign_start'  || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'" 
                  pSortableColumn="scheduled_users">Scheduled Users
                <p-sortIcon field="scheduled_users"></p-sortIcon>
              </th>
            
              <th *ngIf="typeHeaderSurvey == 'compaign_start'  || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'" 
                  pSortableColumn="started_users">Started Users
                <p-sortIcon field="started_users"></p-sortIcon>
              </th>
              <th *ngIf="typeHeaderSurvey == 'compaign_start' || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed'  || typeHeaderSurvey == 'user_details'" 
                  pSortableColumn="completed_users">Completed Users
                <p-sortIcon field="completed_users"></p-sortIcon>
              </th>
              <!-- <th *ngIf="typeHeaderSurvey == 'userAll'" pSortableColumn="total_user_count">Sent Users
                <p-sortIcon field="total_user_count"></p-sortIcon>
              </th> -->
            </tr>        
         <tr *ngIf="showColFilters26" class="scrollFilterTree" >
          <ng-container *ngFor="let col of columns">
          <th  style="background-color: white !important">
            <input style="width:100%"
           style=" text-align:center"  colpsan= "11" placeholder="{{col.field == 'valid' || col.field == 'active' || col.field == 'active_wf' ? 't/f' : '' }}"
            pInputText *ngIf="col.field != 'actions' "
        
            [value]="detailsSurvey.filters[col.field] ? detailsSurvey.filters[col.field].value : ''"
            type="search" class="form-control" (input)="detailsSurvey.filter($event.target.value, col.field, 'contains'); detailsSurvey.resetScrollTop();">
          </th>
        </ng-container>
        </tr>  
     </ng-template>
   <ng-template pTemplate="groupheader" let-row let-rowIndex="rowIndex" let-expanded="expanded">
    <tr style="  background-color: lightgray !important;">
        <td colspan="7" style="vertical-align: middle;" >

  <button style="width: auto; height: 0.5rem ;  " type="button" pButton pRipple [pRowToggler]="row" class="p-button-text p-button-rounded p-button-plain mr-2" [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
          <span  class="truncateshowField" [tooltip]= "row.contract_name"  data-container="body"><b>{{row.contract_name}}</b></span>
          
        </td>        
    </tr>
  <!--  <tr style="  background-color: #f0eeee !important;" *ngIf="typeHeaderSurvey == 'compaign_completed'  || typeHeaderSurvey == 'compaign_start' || typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'  || typeHeaderSurvey == 'compaign_average'" >
      <td></td>
      <td colspan="7" style="vertical-align: middle;">
          <button style="width: auto; height: 0.5rem" type="button" pButton pRipple [pRowToggler]="row" class="p-button-text p-button-rounded p-button-plain mr-2"
              [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
          <span   class="truncateshowField" [tooltip]= "row.campaign_name"  data-container="body"><b>{{row.campaign_name}}</b></span>
      </td>
  </tr>-->
</ng-template>

          <ng-template pTemplate="rowexpansion" let-row  let-i="index">
            <tr >
              <td  *ngIf="typeHeaderSurvey !== 'contract_completed'" ></td>   
              <td  *ngIf="typeHeaderSurvey == 'contract_completed' "  style="text-align:center"  class="truncateModalCampaign" [tooltip]= "row.contract_name"  data-container="body">{{row.contract_name}}</td>             <!-- <td style="text-align:center"  class="truncateModal" [tooltip]= "row.result"  data-container="body">{{row.result}}</td>      --> 
              <td  *ngIf="typeHeaderSurvey == 'compaign_completed'  || typeHeaderSurvey == 'compaign_start' || typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'  || typeHeaderSurvey == 'compaign_average'" style="text-align:center;min-width: 270px;"  class="truncateModalCampaign" [tooltip]= "row.campaign_name"  data-container="body">{{row.campaign_name}}</td>       
             <!--   <td  *ngIf="typeHeaderSurvey == 'compaign_completed'"  style="text-align:center"  class="truncateModal" [tooltip]= "row.result"  data-container="body">{{row.result}}</td>    -->   
           <td *ngIf="typeHeaderSurvey != 'contract_threshold'  && typeHeaderSurvey != 'user_details'  &&  typeHeaderSurvey != 'contract_average'" style="text-align:center"  class="truncateModal" [tooltip]= "row.period"  data-container="body">{{row.period}}</td>      
           <td  *ngIf="typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'contract_average' || typeHeaderSurvey == 'contract_threshold'"  style="text-align:center"  class="truncateModal" [tooltip]= "row.campaign_count || row.total_campaigns"  data-container="body">{{row.campaign_count || row.total_campaigns}}</td>                 
           <td *ngIf="typeHeaderSurvey == 'compaign_threshold' ||  typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'contract_average' ||   typeHeaderSurvey == 'compaign_average'" style="text-align:center"
    [tooltip]="(typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold') ?  (row.calculation_result ? row.calculation_result + '%' : (row.average_result ? row.average_result + '%' : '')) : (row.average_result ? row.average_result + '#' : (row.calculation_result ? row.calculation_result + '#' : ''))" data-container="body">
    {{ (typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold') ? (row.calculation_result ? row.calculation_result + '%' : (row.average_result ? row.average_result + '%' : '')) : (row.average_result ? row.average_result + '#' : (row.calculation_result ? row.calculation_result + '#' : ''))}}
</td>
<td *ngIf="typeHeaderSurvey == 'compaign_threshold'  || typeHeaderSurvey == 'compaign_average' || typeHeaderSurvey == 'contract_threshold'  || typeHeaderSurvey == 'contract_average'"  style="text-align:center"
    [tooltip]="(typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold') ? (row.comparison_operator + ' ' + row.target + ' %') : (row.comparison_operator + ' ' + row.target + ' #')" data-container="body">
     {{(typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold' ) ?(row.comparison_operator + ' ' + row.target + ' %') :(row.comparison_operator + ' ' + row.target + ' #')}}
</td>
   <td *ngIf="typeHeaderSurvey == 'compaign_completed' || typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'contract_completed' ||  typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_average' || typeHeaderSurvey == 'compaign_average'" style="text-align:center" class="truncateModal" [tooltip]="(typeHeaderSurvey == 'compaign_completed' && row.completed_user_count && row.total_user_count) ? (row.completed_user_count + ' / ' + row.total_user_count) : (row.user_participation_count || row.unique_user_count || row.user_count || row.total_users)" data-container="body">
         {{ typeHeaderSurvey == 'compaign_completed' && row.completed_user_count && row.total_user_count ? row.completed_user_count + ' / ' + row.total_user_count : (row.user_participation_count || row.unique_user_count || row.user_count || row.total_users) }}</td>
 
          <!--<td *ngIf="typeHeaderSurvey == 'contract_threshold'" style="text-align:center"  class="truncateModal" [tooltip]= "row.below_threshold_campaigns" data-container="body">{{row.below_threshold_campaigns}}</td> -->      <!--   + '/' + row.total_campaigns /{{row.total_campaigns}}-->   
             
              <!--  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.completed_users"  data-container="body">{{row.completed_users}}/{{row.total_users}}</td> -->      
              <td *ngIf="typeHeaderSurvey == 'compaign_start' || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'" style="text-align:center"  class="truncateModal" [tooltip]= "row.scheduled_users"  data-container="body">{{row.scheduled_users}}</td>   <!--/{{row.total_users}}-->             
              <td *ngIf="typeHeaderSurvey == 'compaign_start' || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed'  || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'" style="text-align:center"  class="truncateModal" [tooltip]= "row.started_users" data-container="body">{{row.started_users}}</td>      <!--/{{row.total_users}}-->
              <td *ngIf="typeHeaderSurvey == 'compaign_start'  || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed'  || typeHeaderSurvey == 'user_details'" style="text-align:center"  class="truncateModal" [tooltip]= "row.completed_users"  data-container="body">{{row.completed_users}}</td>  <!-- [tooltip]= "row.completed_users + '/' + row.total_users" /{{row.total_users}}-->
           <!--   <td *ngIf="typeHeaderSurvey == 'userAll'" style="text-align:center"  class="truncateModal" [tooltip]= "row.total_user_count + '/' + row.total_user_count" data-container="body">{{row.sent_user_count}}/{{row.total_user_count}}</td>      
    -->
              
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="7">No data</td>
        </ng-template>
        </p-table>
        </tab>
     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hideModelSurvey()">Close</button>
 </div>
</div>
</div>
</div>
<!-- END-->


<!-- Treetable survey-->
<div id="detailsTree" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #detailsTree="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-xl" style="height: 90%; max-width : 1200px">
<div class="modal-content">
  <div class="modal-header modalHeaderPrimary">
    <h5 *ngIf="typeHeaderSurvey == 'contract_completed'" class="modal-title" id="createModalLabel">Contracts section: Completed </h5>
    <h5 *ngIf="typeHeaderSurvey == 'contract_onGoing'" class="modal-title" id="createModalLabel">Contracts section: On going/Scheduled</h5>
    <h5 *ngIf="typeHeaderSurvey == 'contract_threshold'" class="modal-title" id="createModalLabel">Contracts section: Below Percentage</h5>
    <h5 *ngIf="typeHeaderSurvey == 'contract_average'" class="modal-title" id="createModalLabel">Contracts section: Below Average</h5>
    <h5 *ngIf="typeHeaderSurvey == 'compaign_completed'" class="modal-title" id="createModalLabel">Campaigns section : Completed </h5>
    <h5 *ngIf="typeHeaderSurvey == 'compaign_start'" class="modal-title" id="createModalLabel">Campaigns section: On going/Scheduled</h5>
    <h5 *ngIf="typeHeaderSurvey == 'compaign_threshold'" class="modal-title" id="createModalLabel">Campaigns section: Below Percentage</h5>
    <h5 *ngIf="typeHeaderSurvey == 'compaign_average'" class="modal-title" id="createModalLabel">Campaigns section: Below Average</h5>
    <h5 *ngIf="typeHeaderSurvey == 'user_tosend'" class="modal-title" id="createModalLabel">Users section: Not completed </h5>
    <h5 *ngIf="typeHeaderSurvey == 'user_details'" class="modal-title" id="createModalLabel">Users section: Details</h5>


  <!-- <h5 *ngIf="typeHeaderSurvey == 'MeasureMaxMin'" class="modal-title" id="createModalLabel">Measure Details </h5>
    <h5 *ngIf="typeHeaderSurvey == 'MeasureAverage'" class="modal-title" id="createModalLabel">Measure Average</h5>
    <h5 *ngIf="typeHeaderSurvey == 'MeasureTop'" class="modal-title" id="createModalLabel">Measure Top 5</h5>
    <h5 *ngIf="typeHeaderSurvey == 'MeasureWrost'" class="modal-title" id="createModalLabel">Measure Wrost 5</h5>--> 
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hideModelSurveyTree()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetWidget> 


       <tab #tab heading="Details" class="table-responsive"  style="padding:unset">

<p-treeTable  #mainTable class="mainTable" [value]="filteredData" [columns]="colsTableSuyrveyTree" styleClass="p-treetable-sm" sortMode="multiple"   (onFilter)="onFilter($event)"   [scrollable]="true" scrollHeight="50vh"
  sortMode="multiple" [multiSortMeta]="[{field: 'contract_name', order: 1},{field: 'campaign_name', order: 1}, {field: 'period', order: 1}]"   >




<ng-template pTemplate="caption">

  <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
    <div>
     
   <!--  <button  class="btn btn-outline-dark ml-2" (click)="toggleExpandCollapse()">
        {{ isExpanded ? 'Collapse All' : 'Expand All' }}
      </button>--> 

      <button class="btn btn-outline-dark ml-2" (click)="toggleExpandCollapse()">
        <i [class.pi-chevron-down]="isExpanded" [class.pi-chevron-right]="!isExpanded" class="pi" style="vertical-align: bottom;"></i>
        {{ isExpanded ? 'Collapse All' : 'Expand All' }}
      </button>
      <button class="btn btn-outline-dark ml-2" (click)="showColFiltersT = !showColFiltersT"><i *ngIf="showColFiltersT" class=" pi pi-filter-slash"></i><i *ngIf="!showColFiltersT" class=" pi pi-filter"></i></button>
     <!-- Count: {{countindicator}}-->
        Contracts: {{ uniqueContractCount }}, Campaigns: {{ uniqueNameCount }}
    </div>
      <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
        <div class="input-group">
          <div class="input-group-prepend">
            <span class="input-group-text" style="min-width: 95px;">Global Search</span>
          </div>
          <input type="search" class="form-control"  pInputText size="30"  [(ngModel)]="inputSearch5" (input)="mainTable.filterGlobal($event.target.value, 'contains'); mainTable.resetScrollTop() " style="width:auto">
        </div>
      </div>
  </div>
</ng-template>
  <ng-template pTemplate="header" let-columns>
    <tr class="stickyHeader" style="white-space: nowrap;">
  <ng-container *ngFor="let col of columns">
    <th style="text-align:center"  [ttSortableColumn]="col.field" [style.width]="col.width">
      {{col.header}}
      <p-treeTableSortIcon [field]="col.field"></p-treeTableSortIcon>
  </th>
</ng-container>
</tr>
<tr *ngIf="showColFiltersT" class="scrollFilterTree" >
  <ng-container *ngFor="let col of columns">
  <th  style="background-color: white !important">
    <input style="width:100%"
   style=" text-align:center"  colpsan= "11" placeholder="{{col.field == 'valid' || col.field == 'active' || col.field == 'active_wf' ? 't/f' : '' }}"
    pInputText *ngIf="col.field != 'actions' "

    [value]="mainTable.filters[col.field] ? mainTable.filters[col.field].value : ''"
    type="search" class="form-control" (input)="mainTable.filter($event.target.value, col.field, 'contains'); mainTable.resetScrollTop();">
  </th>
</ng-container>
</tr>  
<!--<tr *ngIf="showColFiltersT" class="scrollFilterTree">
  <th style="padding: unset; background-color: white !important">
    <input style="width:100% ;text-align:center"  colpsan= "7"
           type="search" class="form-control" 
           [value]="mainTable.filters['contract_name'] ? mainTable.filters['contract_name'].value : ''" 
           (input)="mainTable.filter($event.target.value, 'contract_name', 'contains'); mainTable.resetScrollTop(); ">
</th>
     <th style="padding:unset;background-color: white !important">
         <input style="width:100% ;text-align:center"  colpsan= "7" type="search" class="form-control"
         [value]="mainTable.filters['campaign_name'] ? mainTable.filters['campaign_name'].value : ''" 
          (input)="mainTable.filter($event.target.value, 'campaign_name', 'contains'); mainTable.resetScrollTop(); ">
     </th>

     <th *ngIf="typeHeaderSurvey != 'contract_threshold' && typeHeaderSurvey != 'user_details'  &&  typeHeaderSurvey != 'contract_average'"  style="padding:unset;background-color: white !important">
      <input style="width:100% ;text-align:center"  colpsan= "7" type="search" class="form-control"
      [value]="mainTable.filters['period'] ? mainTable.filters['period'].value : ''" 
      (input)="mainTable.filter($event.target.value, 'period', 'contains'); mainTable.resetScrollTop(); ">
  </th>
 
     <th *ngIf="typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'compaign_completed'  || typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'compaign_average' || typeHeaderSurvey == 'contract_average' " style="padding:unset;background-color: white !important">
         <input style="width:100% ;text-align:center"  colpsan= "7" type="search" class="form-control"
         [value]="mainTable.filters['completed_user_count '] ? mainTable.filters['completed_user_count '].value : ''" 
          (input)="mainTable.filter($event.target.value, 'row.completed_user_count ' , 'contains'); mainTable.resetScrollTop();">
     </th>
     <th *ngIf="typeHeaderSurvey == 'compaign_start'  || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'" style="padding:unset;background-color: white !important">
         <input style="width:100% ;text-align:center"  colpsan= "7" type="search" class="form-control" 
         [value]="mainTable.filters['started_users'] ? mainTable.filters['started_users'].value : ''" 
         (input)="mainTable.filter($event.target.value, 'started_users', 'contains'); mainTable.resetScrollTop(); ($event.target.value)">
     </th>
     <th *ngIf="typeHeaderSurvey == 'compaign_start'  || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details'" style="padding:unset;background-color: white !important">
         <input style="width:100% ;text-align:center"  colpsan= "7" type="search" class="form-control"
         [value]="mainTable.filters['scheduled_users'] ? mainTable.filters['scheduled_users'].value : ''" 
          (input)="mainTable.filter($event.target.value, 'scheduled_users', 'contains'); mainTable.resetScrollTop(); ">
     </th>
     <th *ngIf="typeHeaderSurvey == 'compaign_start'  || typeHeaderSurvey == 'contract_onGoing'|| typeHeaderSurvey == 'contract_completed'  || typeHeaderSurvey == 'user_details'"  style="padding:unset;background-color: white !important">
      <input style="width:100% ;text-align:center"  colpsan= "7" type="search" class="form-control"
      [value]="mainTable.filters['completed_users'] ? mainTable.filters['completed_users'].value : ''" 
       (input)="mainTable.filter($event.target.value, 'completed_users', 'contains'); mainTable.resetScrollTop(); ">
  </th>

  <th *ngIf="typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'contract_average' ||  typeHeaderSurvey == 'compaign_average'"  style="padding:unset;background-color: white !important">
    <input style="width:100% ;text-align:center"  colpsan= "7" type="search" class="form-control"
    [value]="mainTable.filters['calculation_result' || 'average_result'] ? mainTable.filters['calculation_result' || 'average_result'].value : ''" 
     (input)="mainTable.filter($event.target.value, 'calculation_result || average_result', 'contains'); mainTable.resetScrollTop();">
</th>
  <th *ngIf="typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold' ||  typeHeaderSurvey == 'compaign_average'"  style="padding:unset;background-color: white !important">
    <input style="width:100% ;text-align:center"  colpsan= "7" type="search" class="form-control"
    [value]="mainTable.filters['target'] ? mainTable.filters['target'].value : ''" 
     (input)="mainTable.filter($event.target.value, 'target', 'contains'); mainTable.resetScrollTop(); ">
</th>
   
 </tr>-->
</ng-template>
<ng-template pTemplate="body" let-rowNode let-rowData="rowData" let-columns="columns">
  <tr [ngClass]="{'contract-level': rowData.class === 'contract-level', 'assignment-level': rowData.class === 'assignment-level', 'version-level': rowData.class === 'version-level'}" style="height:34px">
    <ng-container *ngFor="let col of columns; let i = index">
    <td style="white-space: nowrap;" *ngIf="col.field != groupByConfig.showField" [style.width]="col.width"  [ngStyle]="{'white-space': i === 0 ? 'nowrap' : 'normal', 'text-align': i === 1 ? 'left' : (i === 0 ? 'left' : 'center')}">
      <ng-container *ngIf="i === 0 && rowData.contract_name">
        <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>          
      </ng-container>   
      <ng-container *ngIf="i === 1 && col.field === 'campaign_name' && rowData.campaign_name">
        <p-treeTableToggler [rowNode]="rowNode" class="toggler-second"></p-treeTableToggler>
      </ng-container>
     <span *ngIf="col.field != 'calculation_result' && col.field != 'target' && col.field != 'completed_user_count'"  [tooltip] = "rowData[col.field]"  class="truncateTree" data-container="body" >{{rowData[col.field]}}</span>       
 

     <span *ngIf="col.field === 'calculation_result' && rowData.calculation_result && typeHeaderSurvey === 'compaign_threshold'"
     style="text-align:center"
     [tooltip]="rowData.calculation_result + ' %'">
    {{ rowData.calculation_result + ' %' }}
</span>


<span *ngIf="col.field === 'calculation_result' && rowData.calculation_result && typeHeaderSurvey === 'compaign_average'"
style="text-align:center"
[tooltip]="rowData.calculation_result + ' #'">
{{ rowData.calculation_result + ' #' }}
</span>


     <span *ngIf="col.field === 'target' && rowData.target && typeHeaderSurvey === 'compaign_threshold'"
     style="text-align:center"
     [tooltip]="rowData.comparison_operator + ' ' + rowData.target + ' %'">
    {{ rowData.comparison_operator + ' ' + rowData.target + ' %' }}
</span>

<!-- Per 'compaign_average' -->
<span *ngIf="col.field === 'target' && rowData.target && typeHeaderSurvey === 'compaign_average'"
     style="text-align:center"
     [tooltip]="rowData.comparison_operator + ' ' + rowData.target + ' #'">
    {{ rowData.comparison_operator + ' ' + rowData.target + ' #' }}
</span>
     <span *ngIf="col.field == 'completed_user_count' && rowData.completed_user_count" style="text-align:center" class="truncateModal" [tooltip]="( rowData.completed_user_count  + ' / ' + rowData.total_user_count)" data-container="body">
      {{ rowData.completed_user_count + ' / ' + rowData.total_user_count  }}</span>
    
 <!--<span  *ngIf="col.field == 'contract_name'" style="text-align:center;min-width: 270px;"  class="truncateModalCampaign" [tooltip]= "rowData.contract_name"  data-container="body">{{rowData.contract_name}}</span>       
      <span  *ngIf="col.field == 'campaign_name'" style="text-align:center;min-width: 270px;"  class="truncateModalCampaign" [tooltip]= "rowData.campaign_name"  data-container="body">{{rowData.campaign_name}}</span>       
    <span *ngIf="col.field == 'period' && (typeHeaderSurvey != 'contract_threshold'  && typeHeaderSurvey != 'user_details'  &&  typeHeaderSurvey != 'contract_average')" style="text-align:center"  class="truncateModal" [tooltip]= "rowData.period"  data-container="body">{{rowData.period}}</span>      
    <span  *ngIf="typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'contract_average' || typeHeaderSurvey == 'contract_threshold'"  style="text-align:center"  class="truncateModal" [tooltip]= "rowData.campaign_count || row.total_campaigns"  data-container="body">{{rowData.campaign_count || rowData.total_campaigns}}</span>                 
    <span *ngIf="typeHeaderSurvey == 'compaign_threshold' ||  typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'contract_average' ||   typeHeaderSurvey == 'compaign_average'" style="text-align:center"
[tooltip]="(typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold') ?  (rowData.calculation_result ? rowData.calculation_result + '%' : (rowData.average_result ? rowData.average_result + '%' : '')) : (rowData.average_result ? rowData.average_result + '#' : (rowData.calculation_result ? rowData.calculation_result + '#' : ''))" data-container="body">
{{ (typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold') ? (rowData.calculation_result ? rowData.calculation_result + '%' : (rowData.average_result ? rowData.average_result + '%' : '')) : (rowData.average_result ? rowData.average_result + '#' : (rowData.calculation_result ? rowData.calculation_result + '#' : ''))}}
</span>
<span *ngIf="typeHeaderSurvey == 'compaign_threshold'  || typeHeaderSurvey == 'compaign_average' || typeHeaderSurvey == 'contract_threshold'"  style="text-align:center"
[tooltip]="(typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold') ? (rowData.comparison_operator + ' ' + rowData.target + ' %') : (rowData.comparison_operator + ' ' + rowData.target + ' #')" data-container="body">
{{(typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_threshold' ) ?(rowData.comparison_operator + ' ' + rowData.target + ' %') :(rowData.comparison_operator + ' ' + rowData.target + ' #')}}
</span>
<span *ngIf="typeHeaderSurvey == 'compaign_completed' || typeHeaderSurvey == 'contract_threshold' || typeHeaderSurvey == 'contract_completed' ||  typeHeaderSurvey == 'compaign_threshold' || typeHeaderSurvey == 'contract_average' || typeHeaderSurvey == 'compaign_average'" style="text-align:center" class="truncateModal" [tooltip]="(typeHeaderSurvey == 'compaign_completed' && rowData.completed_user_count && rowData.total_user_count) ? (rowData.completed_user_count + ' / ' + rowData.total_user_count) : (rowData.user_participation_count || rowData.unique_user_count || rowData.user_count || rowData.total_users)" data-container="body">
  {{ typeHeaderSurvey == 'compaign_completed' && rowData.completed_user_count && rowData.total_user_count ? rowData.completed_user_count + ' / ' + rowData.total_user_count : (rowData.user_participation_count || rowData.unique_user_count || rowData.user_count || rowData.total_users) }}</span>
    <span *ngIf=" col.field == 'started_users' && (typeHeaderSurvey == 'compaign_start' || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed'  || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details')" style="text-align:center"  class="truncateModal" [tooltip]= "rowData.started_users" data-container="body">{{rowData.started_users}}</span>     
       <span *ngIf="col.field == 'scheduled_users' && (typeHeaderSurvey == 'compaign_start' || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed' || typeHeaderSurvey == 'user_tosend'  || typeHeaderSurvey == 'user_details')" style="text-align:center"  class="truncateModal" [tooltip]= "rowData.scheduled_users"  data-container="body">{{rowData.scheduled_users}}</span>   
       <span *ngIf=" col.field == 'completed_users' &&  (typeHeaderSurvey == 'compaign_start'  || typeHeaderSurvey == 'contract_onGoing' || typeHeaderSurvey == 'contract_completed'  || typeHeaderSurvey == 'user_details')" style="text-align:center"  class="truncateModal" [tooltip]= "rowData.completed_users"  data-container="body">{{rowData.completed_users}}</span>-->

 </td>
     
</ng-container>   
</tr>
</ng-template>
<ng-template pTemplate="emptymessage">
  <tr>
      <td style="text-align:center; min-height: 60vh;" [attr.colspan]="13">
        No data to display
      </td>
  </tr>
</ng-template>
</p-treeTable>


</tab>
</tabset>
</div>
</div>
<div class="modal-footer">
<button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hideModelSurveyTree()">Close</button>
</div>
</div>
</div>
</div>
<!-- END-->

 <!-- RAW DATA-->
<div id="rawDataModal"   bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #rawDataModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
  aria-hidden="true"  style="z-index: 150000">
  <div class="modal-dialog modal-lg modal-dialog-centered" style="height: 90%; max-width : 700px">
    <div class="modal-content">
       <div class="modal-header modalHeaderPrimary">
     <h5 class="modal-title" id="approveModalLabel">Raw data - {{headerRawData}}</h5>
     <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="hideRawData()">
       <span aria-hidden="true">&times;</span>
     </button>
   </div>
   <div class="modal-body marginUnset fuseqGreyBack">
     <div class="fuseqTabset-modal">
       <tabset #tabsetModify>
        <tab #1  heading="Details" class="table-responsive"  #scrollContainer  style="padding:unset; overflow-y: scroll !important;">
          <pre  [innerHTML]="viewVar | prettyjson: [false, 3]"></pre>  
      </tab>
       </tabset>
     </div>
   </div>
   <div class="modal-footer">
     <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="hideRawData()">Close</button>
   </div>
  </div>
  </div>
  </div>
<!-- END-->
<!-- EXPIRING MODAL-->

<div id="expiringModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #expiringModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-lg" style="height: 90%; max-width : 1000px">
<div class="modal-content">
  <div class="modal-header modalHeaderPrimary">

   <h5 class="modal-title uc" id="approveModalLabel">Details</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="expiringModalHide()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
    <tabset #tabExpiring>
      <tab #tab1 heading="{{typeDuration === 'numberExpiringContract' ? 'By number of contracts' : typeDuration === 'violatedMetrics' ? 'By violated  metrics' : typeDuration === 'violatedPeriods' ? 'By violated periods' : typeDuration === 'financialPenalties' ? 'By penalties' : 'By month'}}" style="height: 67vh; padding: unset;">
        <p-table id="expiringPeriod"  class="table table-hover evidenceDataTable paramTable" #expiringPeriod  [columns]="colsTableExpiring" [value]="tablePeriodExpiring" [scrollable]="true" scrollHeight="60vh" [globalFilterFields]="['seriesLabel','seriesCount']" [sortMode]="'multiple'" >
           <ng-template pTemplate="caption">
             <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
               <div>
                 <button class="btn btn-outline-dark ml-2" (click)="showColFiltersEx2 = !showColFiltersEx2"><i *ngIf="showColFiltersEx2" class=" pi pi-filter-slash"></i><i *ngIf="!showColFiltersEx2" class=" pi pi-filter"></i></button>
                 Count: {{expiringPeriod.filteredValue?expiringPeriod.filteredValue.length:expiringPeriod.totalRecords}}   
               </div>
                 <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                   <div class="input-group">
                     <div class="input-group-prepend">
                       <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                     </div>
                     <input type="search" class="form-control" [(ngModel)]="inputSearchePeriod"  pInputText size="30" (input)="expiringPeriod.filterGlobal($event.target.value, 'contains'); expiringPeriod.resetScrollTop()" style="width:auto; display: unset;">
                   </div>
                 </div>
             </div>
           </ng-template>
           <ng-template pTemplate="header" let-row let-columns >
             <tr class="fuseqTableHeader" style="white-space: nowrap;">
              <th pSortableColumn="seriesLabel">{{ (typeDuration === 'numberExpiringContract' || typeDuration === 'compliantContract') ? 'Period'  : (typeDuration === 'durationContract' || typeDuration === 'violatedMetrics') ? 'Contract'  : 'Contract' }} <p-sortIcon field="seriesLabel"></p-sortIcon></th>  
              <th  [hidden]= "typeDuration !== 'compliantContract'" pSortableColumn="seriesCompliant">Compliant Contracts<p-sortIcon field="seriesCompliant"></p-sortIcon></th>  
              <th  pSortableColumn="seriesCount">{{ typeDuration === 'numberExpiringContract' ? 'Number of Contracts'  : typeDuration === 'violatedMetrics' ? 'violated metrics' : typeDuration === 'violatedPeriods' ? 'violated periods' : typeDuration === 'durationContract' ? 'Month' : typeDuration === 'financialPenalties' ? 'Penalties'  : 'Total Contracts' }} <p-sortIcon field="seriesCount"></p-sortIcon></th>           
            </tr>  
             <tr *ngIf="showColFiltersEx2" class="scrollFilter" >
               <th  *ngFor="let col of columns" style="background-color: white !important"   [hidden]= "typeDuration !== 'compliantContract' && col.field == 'seriesCompliant'" > 
                <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"       
                style=" text-align:center"  colpsan= "14"
                 placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"       
                 [value]="expiringPeriod.filters[col.field] ? expiringPeriod.filters[col.field].value : ''"
                 type="search" class="form-control" (input)="expiringPeriod.filter($event.target.value, col.field, 'contains'); expiringPeriod.resetScrollTop()">
               </th>
             </tr>
           </ng-template>
           <ng-template pTemplate="body" let-row  let-i="index">
             <tr >
              <td style="text-align:center"  class="truncateModal" [tooltip]= "row.seriesLabel"  data-container="body">{{row.seriesLabel}}</td>  
              <td [hidden]= "typeDuration !== 'compliantContract'"  style="text-align:center"  class="truncateModal" [tooltip]= "row.seriesCompliant"  data-container="body">{{row.seriesCompliant}}</td>    
              <td *ngIf="typeDuration !== 'financialPenalties'"  style="text-align:center"  class="truncateModal" [tooltip]= "row.seriesCount"  data-container="body">{{row.seriesCount}}</td> 
              <td *ngIf="typeDuration === 'financialPenalties'" style="text-align:center" class="truncateModal" [tooltip]="row.seriesCount + ' ' + row.seriesSymbol" data-container="body">{{ row.seriesCount }} {{ row.seriesSymbol }}</td>
            
             </tr>
           </ng-template>
           <ng-template pTemplate="emptymessage">
               <td style="text-align:center" colspan="3">No data</td>
         </ng-template>
         </p-table>
       </tab>
      
      <tab #tab2 *ngIf="typeDuration === 'numberExpiringContract' || typeDuration === 'violatedMetrics' || typeDuration === 'violatedPeriods'" heading="{{typeDuration === 'numberExpiringContract' ? 'By contract party' : typeDuration === 'violatedMetrics' ? 'By contract' : typeDuration === 'violatedPeriods' ? 'By contract': 'By month'}}"
      style="height: 67vh;padding: unset;">  
      <p-table id="expiringWidgetTable" #expiringWidgetTable [value]="tableDataExipring" [scrollable]="true" scrollHeight="60vh" [sortMode]="'multiple'"  [groupRowsBy]="isGroup ? groupByField : null"[(expandedRowKeys)]="expandedRowKeys2"[dataKey]="typeDuration === 'numberExpiringContract' ? 'customer_name' : 'contract_name'"[(expandedRowKeys)]="expandedRowKeys2"[rowGroupMode]="isGroup ? 'subheader' : null" [groupRowsBy]="isGroup ? (typeDuration === 'numberExpiringContract' ? 'customer_name' : 'contract_name') : null"[globalFilterFields]="['customer_name','contract_names','contract_name','period', 'metrics']">
       <ng-template pTemplate="caption">
       <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">         
        <!--<button class="btn btn-outline-dark ml-2" (click)="isGroup = !isGroup">
          {{ isGroup ? 'No Group' : 'By Contract Party' }}
      </button>-->  
       <div>
      <div class="btn-group" dropdown #dropdown="bs-dropdown" [autoClose]="true">
          <select class="form-control" style="width: 100%;"  [(ngModel)]="groupModel" (change)="groupChange($event.target.value, tableDataExipring)">
        <option [value]="true">{{ typeDuration === 'numberExpiringContract' ? 'By Contract Party' :  'By Contract' }}</option>
        <option [value]="false">No Group</option>
      </select>
      </div>
              
             <button *ngIf="groupModel" class="btn btn-outline-dark ml-2" (click)="toggleAllRows2(tableDataExipring, 'violatedMetricsTable')">            
               <i [class.pi-chevron-down]="areAllRowsExpanded2" [class.pi-chevron-right]="!areAllRowsExpanded2" class="pi" style="vertical-align: bottom;"></i>
               {{ areAllRowsExpanded2 ? 'Collapse All' : 'Expand All' }}
           </button>    
             <button class="btn btn-outline-dark ml-2" (click)="showColFiltersEx = !showColFiltersEx"><i *ngIf="showColFiltersEx" class=" pi pi-filter-slash"></i><i *ngIf="!showColFiltersEx" class=" pi pi-filter"></i></button>           
             Contracts: {{expiringWidgetTable.filteredValue?expiringWidgetTable.filteredValue.length:expiringWidgetTable.totalRecords}}
           </div>
           <div class="cold-md-6" style="text-align: right">
             <div class="form-group" style="margin-bottom:unset">
               <div class="input-group">
                 <div class="input-group-prepend">
                   <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                 </div>
                 <input type="search" class="form-control" pInputText size="30" [(ngModel)]="inputSearchExpiring"  (input)="expiringWidgetTable.filterGlobal($event.target.value, 'contains'); expiringWidgetTable.resetScrollTop()" style="width:auto">      
               </div>
             </div>
           </div>
         </div>
       </ng-template>     
       <ng-template pTemplate="header">
             <tr>
                 <th *ngIf="typeDuration === 'numberExpiringContract'" pSortableColumn="customer_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                     Contract Party<p-sortIcon field="customer_name"></p-sortIcon>
                 </div></th>
                 <th *ngIf="typeDuration === 'numberExpiringContract'" pSortableColumn="contract_names" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                   Contract<p-sortIcon field="contract_names"></p-sortIcon>
                 </div></th>
                 <th  *ngIf="typeDuration === 'violatedMetrics' || typeDuration === 'violatedPeriods'" pSortableColumn="contract_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                  Contract<p-sortIcon field="contract_name"></p-sortIcon>
                </div></th>
                 <th pSortableColumn="period" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                   Period<p-sortIcon field="period"></p-sortIcon>
               </div></th>      
               <th *ngIf="typeDuration === 'violatedMetrics'" pSortableColumn="metrics" style="padding:unset;"><div class="flex justify-content-center align-items-center">
                Metrics<p-sortIcon field="metrics"></p-sortIcon>
            </div></th> 
             </tr>
             <tr *ngIf="showColFiltersEx">
                 <th style="padding:unset;background-color: white !important" [hidden] = "typeDuration !== 'numberExpiringContract'">
                 <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="customer_name" class="form-control" (input)="expiringWidgetTable.filter($event.target.value, ['customer_name'] , 'contains')">     
                 </th >
                 <th style="padding:unset;background-color: white !important" [hidden] = "typeDuration !== 'numberExpiringContract'">
                     <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="contract_names" class="form-control" (input)="expiringWidgetTable.filter($event.target.value, ['contract_names'] , 'contains')">     
                 </th>
                 <th style="padding:unset;background-color: white !important" [hidden] = "typeDuration !== 'violatedMetrics' && typeDuration !== 'violatedPeriods'">
                  <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="contract_name" class="form-control" (input)="expiringWidgetTable.filter($event.target.value, ['contract_name'] , 'contains')">     
              </th>
                 <th style="padding:unset;background-color: white !important">
                   <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="period" class="form-control" (input)="expiringWidgetTable.filter($event.target.value, ['period'] , 'contains')">
                   </th>
                   <th style="padding:unset;background-color: white !important"  [hidden] = "typeDuration !== 'violatedMetrics'">
                    <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="metrics" class="form-control" (input)="expiringWidgetTable.filter($event.target.value, ['metrics'] , 'contains')">
                    </th>
             </tr>
         </ng-template>
         <ng-template pTemplate="groupheader" *ngIf="isGroup" let-customer let-rowIndex="rowIndex" let-expanded="expanded">
          <tr style="  background-color: lightgray !important;">
              <td *ngIf="typeDuration === 'numberExpiringContract'" colspan="7" style="vertical-align: middle;" >
                  <button style="width: auto; height: 0.5rem" type="button" pButton pRipple [pRowToggler]="customer" class="p-button-text p-button-rounded p-button-plain mr-2" [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                  <span  class="truncateshowField" [tooltip]= "customer.customer_name" data-container="body"><b>{{customer.customer_name}}</b>  </span>
              </td>
              <td  *ngIf="typeDuration === 'violatedMetrics' || typeDuration === 'violatedPeriods'" colspan="7" style="vertical-align: middle;" >
                  <button style="width: auto; height: 0.5rem" type="button" pButton pRipple [pRowToggler]="customer" class="p-button-text p-button-rounded p-button-plain mr-2" [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                  <span  class="truncateshowField" [tooltip]= "customer.contract_name" data-container="body"><b>{{customer.contract_name}}</b>  </span>
              </td>
          </tr>
      </ng-template>
         <ng-template pTemplate="rowexpansion" let-customer>
             <tr>
                 <td *ngIf="isGroup"></td>   
                 <td *ngIf="!isGroup && typeDuration === 'numberExpiringContract'" style="text-align: center;" [tooltip]= "customer.customer_name" data-container="body">{{customer.customer_name}}</td>  
                 <td *ngIf="!isGroup && (typeDuration === 'violatedMetrics' || typeDuration === 'violatedPeriods')" style="text-align: center;" [tooltip]= "customer.contract_name" data-container="body">{{customer.contract_name}}</td>            
                 <td *ngIf="typeDuration === 'numberExpiringContract'" style="text-align: center;" [tooltip]= "customer.contract_names" data-container="body">{{customer.contract_names}}</td>
                 <td style="text-align: center;" [tooltip]= "customer.period" data-container="body"> {{customer.period }}</td>     
                 <td  *ngIf="typeDuration === 'violatedMetrics'" style="text-align: center;" [tooltip]= "customer.metrics" data-container="body"> {{customer.metrics }}</td>          
             </tr>
         </ng-template>
     </p-table>
   </tab>
  
</tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="expiringModalHide()">Close</button>
 </div>
</div>
</div>
</div>
<!-- END-->
<div id="MetricByContractModal" bsModal [config]="{ignoreBackdropClick: true, keyboard: false}" #MetricByContractModal="bs-modal" class="modal fade bs-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="classInfo"
aria-hidden="true" style="z-index: 15000">
<div class="modal-dialog  modal-dialog modal-xl" style="height: 90%; max-width : 1300px">
<div class="modal-content">
 <!-- <div class="modal-header modalHeaderPrimary">-->
<div [ngClass]="typeHeaderMetric ==='Compliant' ? 'modal-header modalHeaderCreate' : (typeHeaderMetric ==='Not Compliant' || typeHeaderMetric ==='violatedMetrics'  || typeHeaderMetric ==='violatedPeriods' || typeHeaderMetric ==='financialPenalties')  ? 'modal-header modalHeaderDanger' : (typeHeaderMetric ==='Not Calculated' || typeHeaderMetric ==='Pending') ? 'modal-header modalHeaderModify' : 'modal-header modalHeaderPrimary'" >
   <h5 class="modal-title uc" id="approveModalLabel">{{headerOnchart}}</h5>
   <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="MetricByContractModalHide()">
     <span aria-hidden="true">&times;</span>
   </button>
 </div>
 <div class="modal-body marginUnset fuseqGreyBack">
   <div class="fuseqTabset-modal">
     <tabset #tabsetWidgetMetricByContractModal>
       <tab    #tab heading="Metrics" class="table-responsive"  style="padding:unset">
        <p-table *ngIf="typeHeaderMetric === 'Compliant' || typeHeaderMetric === 'Not Compliant' || typeHeaderMetric === 'violatedMetrics' || typeHeaderMetric === 'financialPenalties' || typeHeaderMetric === 'Pending' || typeHeaderMetric === 'Not Calculated' || typeHeaderMetric === 'Others'" id="metricsByContractTable"  class="table table-hover evidenceDataTable paramTable" #metricsByContractTable  [columns]="colsMetricsbyContract" [value]="recordMetricsTable" [scrollable]="true" scrollHeight="60vh"
        (sortFunction)="customSort($event)" [customSort]="true">
          <ng-template pTemplate="caption">
            <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">
              <div>
                <button class="btn btn-outline-dark ml-2" (click)="showColFiltersMetrics = !showColFiltersMetrics"><i *ngIf="showColFiltersMetrics" class=" pi pi-filter-slash"></i><i *ngIf="!showColFiltersMetrics" class=" pi pi-filter"></i></button>
                Count: {{metricsByContractTable.filteredValue?metricsByContractTable.filteredValue.length:metricsByContractTable.totalRecords}} 
                
              </div>
                <div class="form-group cold-md-6" style="text-align: right; margin-bottom:unset">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                    </div>
                    <input type="search" class="form-control"  pInputText size="30" (input)="metricsByContractTable.filterGlobal($event.target.value, 'contains'); metricsByContractTable.resetScrollTop()" style="width:auto">
                  </div>
                </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header" let-row let-columns >
                <tr class="fuseqTableHeader" style="white-space: nowrap;">
                  <th *ngIf="typeHeaderMetric ==='violatedMetrics'  || typeHeaderMetric ==='violatedPeriods' || typeHeaderMetric ==='financialPenalties' || hiddeAction" pSortableColumn="period">period<p-sortIcon field="period"></p-sortIcon></th>  
                  <th pSortableColumn="process_name">process<p-sortIcon field="process_name"></p-sortIcon></th>  
                  <th pSortableColumn="customer_name">Main Contract Party<p-sortIcon field="customer_name"></p-sortIcon></th>  
                  <th pSortableColumn="indicator_name">metric<p-sortIcon field="indicator_name"></p-sortIcon></th>  
                  <th pSortableColumn="result"> {{typeHeaderMetric === 'financialPenalties' ? 'Penalty' : 'result' }}<p-sortIcon field="result"></p-sortIcon></th>   
                  <th *ngIf="typeHeaderMetric !=='financialPenalties'" pSortableColumn="target">target<p-sortIcon field="target"></p-sortIcon></th>              
              <!--    <th  pSortableColumn="compliant">compliant<p-sortIcon field="compliant"></p-sortIcon></th>   -->                  
                  <th  pSortableColumn="partial_period">complete<p-sortIcon field="partial_period"></p-sortIcon></th>  
                  <th *ngIf="typeHeaderMetric ==='violatedMetrics'  || typeHeaderMetric ==='violatedPeriods' || typeHeaderMetric ==='financialPenalties'"  pSortableColumn="tracking_period">tracking period<p-sortIcon field="tracking_period"></p-sortIcon></th>  
                  <th>actions</th>   
                </tr>
                <tr *ngIf=" showColFiltersMetrics" class="scrollFilterTree" >
                 <th  *ngFor="let col of columns" style="background-color: white !important"  
                 [hidden]= "((col.field =='period') && (typeHeaderMetric !== 'violatedMetrics' && !hiddeAction  && typeHeaderMetric !== 'violatedPeriods' && typeHeaderMetric !== 'financialPenalties')) || ((col.field =='tracking_period') && (typeHeaderMetric !== 'violatedMetrics' && typeHeaderMetric !== 'violatedPeriods' && typeHeaderMetric !== 'financialPenalties')) || (col.field =='target' && typeHeaderMetric == 'financialPenalties')">
                  <div  class="gridster-item-content">    <input style="width:100%"  pInputText *ngIf="col.field != 'actions' && col.field != 'checkbox'"
                   style=" text-align:center"  colpsan= "14"
                    placeholder="{{col.field == 'active'  ? 't/f' : '' ||  col.field == 'partial_period'  ? 't/f' : '' ||  col.field == 'compliant'  ? 't/f' : '' }}"
    
                    [value]="metricsByContractTable.filters[col.field] ? metricsByContractTable.filters[col.field].value : ''"
                    type="search" class="form-control" (input)="metricsByContractTable.filter($event.target.value, col.field, 'contains'); metricsByContractTable.resetScrollTop()"></div>
                  </th>
                </tr>
              </ng-template>
                  <ng-template pTemplate="body" let-row  let-i="index">
                    <tr >
                      <td *ngIf="typeHeaderMetric ==='violatedMetrics'  || typeHeaderMetric ==='violatedPeriods' || typeHeaderMetric ==='financialPenalties  || hiddeAction'" style="text-align:center"  class="truncateModal" [tooltip]= "row.period" data-container="body">{{row.period}}</td> 
                      <td style="text-align:center"  class="truncateModalCampaign" [tooltip]= "row.process_name"  data-container="body">{{row.process_name}}</td>   
                      <td style="text-align:center"  class="truncateModalCampaign" [tooltip]= "row.customer_name"  data-container="body">{{row.customer_name}}</td>   
                      <td style="text-align:center"  class="truncateModalCampaign" [tooltip]= "row.indicator_name"  data-container="body">{{row.indicator_name}}</td>   
                      <td *ngIf="typeHeaderMetric !=='financialPenalties'" style="text-align:center"  class="truncateModal" [tooltip]= "row.result" data-container="body">{{row.result}}</td>                     
                   <td *ngIf="typeHeaderMetric ==='financialPenalties'" style="text-align:center"  class="truncateModal" [tooltip]= "row.result + row.currency" data-container="body">{{row.result}} {{row.currency}}</td>   
                      <td  *ngIf="typeHeaderMetric !=='financialPenalties'" style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>          
                      <td style="text-align:center">
                        <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                        <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
                      </td>
                      <td  *ngIf="typeHeaderMetric ==='violatedMetrics'  || typeHeaderMetric ==='violatedPeriods' || typeHeaderMetric ==='financialPenalties'" style="text-align:center"  class="truncateModal" [tooltip]= "row.tracking_period"  data-container="body">{{row.tracking_period}}</td>  
                      <td style="text-align:center">
                        <button *ngIf="row.partial_period !== null && !hiddeAction" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'drill')">
                          <i class="fa-solid fa-folder-open"></i>
                        </button>
                        <button *ngIf="row.partial_period === null && !hiddeAction" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                          <i class="fa-solid fa-folder-open"></i>
                        </button>
                      </td>
                    </tr>
                  </ng-template>
          <ng-template pTemplate="emptymessage">
              <td style="text-align:center" colspan="7">No data</td>
        </ng-template>
        </p-table>     
       <p-table *ngIf="typeHeaderMetric === 'violatedPeriods'" id="widgetBarViolatedPeriods" #widgetBarViolatedPeriods [value]="recordMetricsTable" [scrollable]="true" scrollHeight="60vh" [sortMode]="'multiple'" 
         [groupRowsBy]="isGroupPeriods ? groupByField : null"[(expandedRowKeys)]="expandedRowKeysPeriods"
         [dataKey]="typeHeaderMetric === 'violatedPeriods' ? 'period' : 'period'"
         [(expandedRowKeys)]="expandedRowKeysPeriods"[rowGroupMode]="isGroupPeriods ? 'subheader' : null" 
         [groupRowsBy]="isGroupPeriods ? (typeHeaderMetric === 'violatedPeriods' ? 'period' : 'period') : null"
         [globalFilterFields]="['customer_name','indicator_name','result','period', 'tracking_period', 'target','partial_period' ]">     
         <ng-template pTemplate="caption">
         <div class="col-md-12" style="place-content:space-between;display:flex;padding:unset">         
         <div>
               <button *ngIf="groupModelPeriods" class="btn btn-outline-dark ml-2" (click)="toggleAllRows2(recordMetricsTable, 'violatedPeriodsBar')">            
                 <i [class.pi-chevron-down]="areAllRowsExpandedPeriods" [class.pi-chevron-right]="!areAllRowsExpandedPeriods" class="pi" style="vertical-align: bottom;"></i>
                 {{ areAllRowsExpandedPeriods ? 'Collapse All' : 'Expand All' }}
             </button>    
               <button class="btn btn-outline-dark ml-2" (click)="showColFiltersEx = !showColFiltersEx"><i *ngIf="showColFiltersEx" class=" pi pi-filter-slash"></i><i *ngIf="!showColFiltersEx" class=" pi pi-filter"></i></button>           
               Periods: {{widgetBarViolatedPeriods.filteredValue?widgetBarViolatedPeriods.filteredValue.length:widgetBarViolatedPeriods.totalRecords}}
             </div>
             <div class="cold-md-6" style="text-align: right">
               <div class="form-group" style="margin-bottom:unset">
                 <div class="input-group">
                   <div class="input-group-prepend">
                     <span class="input-group-text" style="min-width: 95px;">Global Search</span>
                   </div>
                   <input type="search" class="form-control" pInputText size="30" [(ngModel)]="inputSearchExpiring"  (input)="widgetBarViolatedPeriods.filterGlobal($event.target.value, 'contains'); widgetBarViolatedPeriods.resetScrollTop()" style="width:auto">      
                 </div>
               </div>
             </div>
           </div>
         </ng-template>     
         <ng-template pTemplate="header">
               <tr style="white-space: nowrap;">     
                <th pSortableColumn="period" style="padding:unset;"><div class="flex justify-content-center align-items-center">Period<p-sortIcon field="period"></p-sortIcon></div></th>           
                   <th pSortableColumn="indicator_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">Metric<p-sortIcon field="indicator_name"></p-sortIcon></div></th>                
                   <th pSortableColumn="process_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">process<p-sortIcon field="process_name"></p-sortIcon> </div></th>  
                  <th pSortableColumn="customer_name" style="padding:unset;"><div class="flex justify-content-center align-items-center">Main Contract Party<p-sortIcon field="customer_name"></p-sortIcon> </div></th>  
                  <th pSortableColumn="result" style="padding:unset;"> <div class="flex justify-content-center align-items-center">{{typeHeaderMetric === 'financialPenalties' ? 'Penalty' : 'result' }}<p-sortIcon field="result"></p-sortIcon> </div></th>   
                  <th *ngIf="typeHeaderMetric !=='financialPenalties'" pSortableColumn="target" style="padding:unset;"><div class="flex justify-content-center align-items-center">target<p-sortIcon field="target"></p-sortIcon> </div></th>                                  
                  <th  pSortableColumn="partial_period" style="padding:unset;"><div class="flex justify-content-center align-items-center">complete<p-sortIcon field="partial_period"></p-sortIcon> </div></th>  
                  <th  pSortableColumn="tracking_period" style="padding:unset;"><div class="flex justify-content-center align-items-center">tracking period<p-sortIcon field="tracking_period"></p-sortIcon> </div></th>  
                  <th><div class="flex justify-content-center align-items-center">actions</div></th>                 
               </tr>
               <tr *ngIf="showColFiltersEx">
                   <th style="padding:unset;background-color: white !important" >
                   <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="indicator_name" class="form-control" (input)="widgetBarViolatedPeriods.filter($event.target.value, ['indicator_name'] , 'contains')">     
                   </th >  
                   <th style="padding:unset;background-color: white !important" >
                    <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="process_name" class="form-control" (input)="widgetBarViolatedPeriods.filter($event.target.value, ['process_name'] , 'contains')">     
                    </th >  
                    <th style="padding:unset;background-color: white !important" >
                      <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="customer_name" class="form-control" (input)="widgetBarViolatedPeriods.filter($event.target.value, ['customer_name'] , 'contains')">     
                      </th >  
                      <th style="padding:unset;background-color: white !important" >
                        <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="result" class="form-control" (input)="widgetBarViolatedPeriods.filter($event.target.value, ['result'] , 'contains')">     
                        </th >  
                        <th style="padding:unset;background-color: white !important" >
                          <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="target" class="form-control" (input)="widgetBarViolatedPeriods.filter($event.target.value, ['target'] , 'contains')">     
                          </th >  
                          <th style="padding:unset;background-color: white !important" >
                            <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="partial_period" class="form-control" (input)="widgetBarViolatedPeriods.filter($event.target.value, ['partial_period'] , 'contains')">     
                            </th >                      
                   <th style="padding:unset;background-color: white !important">
                     <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="period" class="form-control" (input)="widgetBarViolatedPeriods.filter($event.target.value, ['period'] , 'contains')">
                     </th>        
                     <th style="padding:unset;background-color: white !important" >
                      <input style="padding: unset;font-size: 12px;margin: 0px;" type="text" required  id="tracking_period" class="form-control" (input)="widgetBarViolatedPeriods.filter($event.target.value, ['tracking_period'] , 'contains')">     
                      </th >         
               </tr>
           </ng-template>
           <ng-template pTemplate="groupheader" *ngIf="isGroupPeriods" let-row let-rowIndex="rowIndex" let-expanded="expanded">
            <tr style="  background-color: lightgray !important;">
                <td colspan="9" style="vertical-align: middle;" >
                    <button style="width: auto; height: 0.5rem" type="button" pButton pRipple [pRowToggler]="row" class="p-button-text p-button-rounded p-button-plain mr-2" [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                    <span  class="truncateshowField" [tooltip]= "row.period" data-container="body"><b>{{row.period}}</b>  </span>
                </td>
            </tr>
        </ng-template>
           <ng-template pTemplate="rowexpansion" let-row>
               <tr>
                   <td *ngIf="isGroupPeriods"></td>   
                   <td *ngIf="!isGroupPeriods" style="text-align:center" class="truncateModalCampaign"[tooltip]= "row.period" data-container="body">{{row.period}}</td>   
                   <td  style="text-align: center;" [tooltip]= "row.indicator_name" data-container="body">{{row.indicator_name}}</td>                                               
                   <td style="text-align:center"  class="truncateModalCampaign" [tooltip]= "row.process_name"  data-container="body">{{row.process_name}}</td>   
                   <td style="text-align:center"  class="truncateModalCampaign" [tooltip]= "row.customer_name"  data-container="body">{{row.customer_name}}</td>
                   <td *ngIf="typeHeaderMetric !=='financialPenalties'" style="text-align:center"  class="truncateModal" [tooltip]= "row.result" data-container="body">{{row.result}}</td>                     
                   <td *ngIf="typeHeaderMetric ==='financialPenalties'" style="text-align:center"  class="truncateModal" [tooltip]= "row.result + row.currency" data-container="body">{{row.result}} {{row.currency}}</td>   
                   <td  *ngIf="typeHeaderMetric !=='financialPenalties'" style="text-align:center"  [tooltip]= "row.comparison_operator + ' ' + row.target + ' ' + row.unitMeasureSymbol"   data-container="body" >{{row.comparison_operator}} {{row.target}} {{row.unitMeasureSymbol}}</td>          
                   <td style="text-align:center">
                    <i *ngIf="row.partial_period == true" class="fa fa-times fq-danger-color"></i>
                    <i *ngIf="row.partial_period == false" class="fa fa-check fq-success-color"></i>
                  </td>
                  <td style="text-align:center"  class="truncateModal" [tooltip]= "row.tracking_period" data-container="body">{{row.tracking_period}}</td>   
                  <td style="text-align:center">
                    <button *ngIf="row.partial_period !== null" class="btn btn-outline-primary btn-sm" style="font-size: 10px;"  tooltip="Raw Data" data-container="body" data-target="#detailsModal" (click)="getRawData(row, 'drill')">
                      <i class="fa-solid fa-folder-open"></i>
                    </button>
                    <button *ngIf="row.partial_period === null" class="btn btn-outline-primary btn-sm" style="visibility: hidden; font-size: 10px;">
                      <i class="fa-solid fa-folder-open"></i>
                    </button>
                  </td>
                  </tr>
           </ng-template>
       </p-table>
      </tab>
     </tabset>
   </div>
 </div>
 <div class="modal-footer">
   <button type="button" class="btn btn-danger" data-dismiss="modal" (click)="MetricByContractModalHide()">Close</button>
 </div>
</div>
</div>
</div>