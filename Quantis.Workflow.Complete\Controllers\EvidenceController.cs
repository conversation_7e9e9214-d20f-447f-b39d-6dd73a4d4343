using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Quantis.WorkFlow.Services;
using Quantis.WorkFlow.Services.API;
using Quantis.WorkFlow.Services.DTOs.Evidence;
using Quantis.WorkFlow.Services.DTOs.CLM;
using Quantis.WorkFlow.Services.DTOs.Evidence.JSONHelpers;
using Quantis.WorkFlow.Services.Framework;
using System.Collections.Generic;
using System;
using System.IO;
using System.Data;
using System.Reflection;
using DataTable = System.Data.DataTable;
using SpreadsheetLight;
using SpreadsheetLight.Charts;
using System.Text.Json;



namespace Quantis.Workflow.Complete.Controllers
{
    [Produces("application/json")]
    [Route("api/[controller]")]
    [ApiController]
    [EnableCors("AllowOrigin")]

    public class EvidenceController : ControllerBase
    {
        private IEvidenceServices _evidenceAPI { get; set; }
        public EvidenceController(IEvidenceServices EvidenceAPI)
        {
            _evidenceAPI = EvidenceAPI;
        }
        [HttpGet("convertJS")]
        public string convertJs(string input, string starttz, string endtz)
        {
            //var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.convertJS(input, starttz, endtz);
        }
        [HttpGet("Get_EvidenceCalculationParameters")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCalculationParameterDTO> Get_EvidenceCalculationParameters()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCalculationParameters(usr); 
        }

        [HttpGet("Get_EvidenceCalculationParameters_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCalculationParameterDTO> Get_EvidenceCalculationParameters_ById(int id_param)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCalculationParameters_ById(id_param, usr); 
        }

        [HttpGet("Get_EvidenceCalculationParameters_ByContract")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCalculationParameterDTO> Get_EvidenceCalculationParameters_ByContract(int id_contract)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCalculationParameters_ByContract(id_contract, usr); 
        }

        [HttpGet("Get_EvidenceCalculationParameters_ByIndicator")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCalculationParameterDTO> Get_EvidenceCalculationParameters_ByIndicator(int id_indicator)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCalculationParameters_ByMetric(id_indicator, usr);
        }

        [HttpGet("Get_EvidenceCalculationParameter_ByVersion")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCalculationParameterDTO> Get_EvidenceCalculationParameter_ByVersion(int id_version)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCalculationParameter_ByVersion(id_version, usr); 
        }

        [HttpGet("Get_EvidenceCalculationParameters_Versions")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<E_CalculationParameterVersionDTO> Get_EvidenceCalculationParameters_Versions(int id_parameter)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCalculationParameters_Versions(id_parameter, usr); 
        }

        [HttpPost("Create_EvidenceCalculationParameter")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceCalculationParameterDTO Create_EvidenceCalculationParameter(EvidenceCalculationParameterDTO parameter)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Create_EvidenceCalculationParameter(parameter, usr); 
        }

        [HttpPost("Update_EvidenceCalculationParameter")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceCalculationParameter(EvidenceCalculationParameterDTO parameter)
        {
            return _evidenceAPI.Update_EvidenceCalculationParameter(parameter); 
        }

        [HttpPost("Delete_EvidenceCalculationParameter_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceCalculationParameter_ById(int id_parameter)
        {
            return _evidenceAPI.Delete_EvidenceCalculationParameter_ById(id_parameter); 
        }

        [HttpPost("Delete_EvidenceCalculationParameter_ByVersion")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceCalculationParameter_ByVersion(int id_version)
        {
            return _evidenceAPI.Delete_EvidenceCalculationParameter_ByVersion(id_version); 
        }

        [HttpGet("Get_EvidenceContracts")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceContractDTO> Get_EvidenceContracts()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceContracts(usr); 
        }

        [HttpGet("check-licensingSurvey")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool CheckLicensingAvailability(int id)  
        //id del contratto per capire se aumentano oppure no 
        //il numero di contratti collegati alle survey
        //se le licenze sono sufficienti per creare una nuova survey
        {
            return _evidenceAPI.canCreateSurvay(id); 
        }
        //-----post per registrare i kpi creati per una survey
        [HttpPost("registerSurvey")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool registerSurvey(CompaignDTO compaign)
        {
            // Verifica che HttpContext e User non siano null
            Console.WriteLine("Inizio metodo registerSurvey");

            if (HttpContext?.User == null)
            {
                Console.WriteLine("HttpContext.User è null");
                throw new Exception("L'utente non è autenticato.");
            }
            
            if (compaign == null)
            {
                Console.WriteLine("CompaignDTO è null");
                throw new ArgumentNullException(nameof(compaign), "Il parametro compaign è null.");
            }
            var result = _evidenceAPI.registerSurvey(compaign);

            if (result == null)
            {
                Console.WriteLine("Errore durante la registrazione del survey");
                throw new Exception("Errore durante la registrazione del survey.");
            }
            return result;
        }

        [HttpGet("CheckIndicatorCalculation")]
        //[Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public IActionResult CheckIndicatorCalculation(int id)
        {  
            // CHECK LICENCE non viene chiamata 31/10/2024 
            var result = _evidenceAPI.CheckIndicatorCalculation(id);
            return Ok(new 
            { 
                isCalculated = result.GetIsCalculated(), 
                canCalculate = result.GetCanCalculate() 
            });
        }


        [HttpGet("Get_EvidenceContract_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceContractDTO> Get_EvidenceContract_ById(int id_contract)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceContract_ById(id_contract, usr);
        }

        [HttpGet("Get_EvidenceContract_ByVersion")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceContractDTO> Get_EvidenceContract_ByVersion(int id_version)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceContract_ByVersion(id_version, usr); 
        }
        [HttpPost("Duplicate_EvidenceContract_Indicators")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceContractDTO Duplicate_EvidenceContract_Indicators(EvidenceContractDTO contract)
        {
            var usr = (HttpContext.User) as AuthUser;
            var UserId = usr.UserId;
            var SessionToken = usr.SessionToken;
            return _evidenceAPI.Duplicate_EvidenceContract_Indicators(contract, UserId, SessionToken, usr); 
        }
        
        [HttpGet("Get_EvidenceContracts_ByCustomer")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceContractDTO> Get_EvidenceContracts_ByCustomer(int id_customer)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceContracts_ByCustomer(id_customer, usr); 
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceContracts_ByService")]
        public List<EvidenceContractDTO> Get_EvidenceContracts_ByService(int id_service)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceContracts_ByService(id_service, usr);
        }

        [HttpPost("Create_EvidenceContract")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceContractDTO Create_EvidenceContract(EvidenceContractDTO contract)
        {
            var usr = (HttpContext.User) as AuthUser;
            var UserId = usr.UserId;
            var SessionToken = usr.SessionToken;
            return _evidenceAPI.Create_EvidenceContract(contract, UserId, SessionToken, usr); 
        }

        [HttpPost("Update_EvidenceContract")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceContract(EvidenceContractDTO contract)
        {
            return _evidenceAPI.Update_EvidenceContract(contract); 
        }

        [HttpPost("Delete_EvidenceContract_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceContract_ById(int id_contract)
        {
            return _evidenceAPI.Delete_EvidenceContract_ById(id_contract); 
        }

        [HttpPost("Delete_EvidenceContract_ByVersion")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceContract_ByVersion(int id_version)
        {
            return _evidenceAPI.Delete_EvidenceContract_ByVersion(id_version); 
        }

        [HttpGet("Get_EvidenceCustomers")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCustomerDTO> Get_EvidenceCustomers()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCustomers(usr); 
        }

        [HttpGet("Get_EvidenceCustomer_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCustomerDTO> Get_EvidenceCustomer_ById(int id_customer)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCustomer_ById(id_customer, usr);
        }

        [HttpGet("Get_EvidenceCustomer_ByVersion")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCustomerDTO> Get_EvidenceCustomer_ByVersion(int id_version)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCustomer_ByVersion(id_version, usr); 
        }

        [HttpGet("Get_EvidenceCustomers_ByTenant")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCustomerDTO> Get_EvidenceCustomers_ByTenant(int id_tenant)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCustomers_ByTenant(id_tenant, usr); 
        }

        [HttpPost("Create_EvidenceCustomer")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceCustomerDTO Create_EvidenceCustomer(EvidenceCustomerDTO customer)
        {
            var usr = (HttpContext.User) as AuthUser;
            var UserId = usr.UserId;
            var SessionToken = usr.SessionToken;
            return _evidenceAPI.Create_EvidenceCustomer(customer, UserId, SessionToken, usr); 
        }

        [HttpPost("Decript_licence")]       //add licence
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Decript_licence(EvidenceLicenceDTO licence)
        {
            Console.WriteLine("Decript_licence:: ", licence.ToString());
            return _evidenceAPI.Create_Licence(licence); 
        }

        [HttpPost("Decript_token_licence")]
  
        public bool Decript_token_licence(EvidenceLicenceDTO licence)
        {
            Console.WriteLine("Decript_token_licence:: ", licence.ToString());
            return _evidenceAPI.Decript_token_licence(licence); 
        }

        [HttpGet("Get_Evidence_Licence")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceLicenceDTO> Get_Evidence_Licence ()
        {
            return _evidenceAPI.Get_Evidence_Licence();
        }

        //Contract
        [HttpGet("Get_Evidence_licence_contract")]     
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public ResponseIsLicensed Get_Evidence_licence_contract ()
        {
            return _evidenceAPI.Get_Evidence_licence_contract();
        }

        //Experience 
        [HttpGet("Get_Evidence_licence_survey")]     
        //[Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public ResponseIsLicensed Get_Evidence_licence_survey ()
        {
            return _evidenceAPI.Get_Evidence_licence_survey();
        }

        //Performance     
        [HttpGet("Get_Evidence_licence_indicator")]     
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public ResponseIsLicensed Get_Evidence_licence_indicator ()
        {
            return _evidenceAPI.Get_Evidence_licence_indicator();
        }
        

        [HttpPost("Update_EvidenceCustomer")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceCustomer(EvidenceCustomerDTO customer)
        {
            return _evidenceAPI.Update_EvidenceCustomer(customer); 
        }

        [HttpPost("Delete_EvidenceCustomer_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceCustomer_ById(int id_customer)
        {
            return _evidenceAPI.Delete_EvidenceCustomer_ById(id_customer); 
        }

        [HttpPost("Delete_EvidenceCustomer_ByVersion")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceCustomer_ByVersion(int id_version)
        {
            return _evidenceAPI.Delete_EvidenceCustomer_ByVersion(id_version); 
        }

        [HttpGet("Get_EvidenceIndicators")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceIndicators()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics(usr); 
        }

        [HttpGet("Get_EvidenceIndicator_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceIndicator_ById(int id_indicator)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetric_ById(id_indicator, usr); 
        }
        
        [HttpGet("Get_EvidenceIndicator_ByVersion")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceIndicator_ByVersion(int id_version)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetric_ByVersion(id_version, usr); 
        }
        [HttpGet("Get_EvidenceThresholdsbyContracts")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<Thresholds_ContractDTO> Get_EvidenceThresholdsbyContracts(int id_contract)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceThresholdsbyContracts(id_contract, usr); 
        }
        [HttpGet("Get_EvidenceIndicator_ByContract")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceIndicator_ByContract(int id_contract)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics_ByContract(id_contract, usr); 
        }
        [HttpGet("Get_EvidenceMetrics_ByContract_WFAssigned")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceMetrics_ByContract_WFAssigned(int id_contract, int id_wftemplate)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics_ByContract_WFAssigned(id_contract, id_wftemplate, usr); 
        }
        [HttpGet("Get_EvidenceIndicators_ByReferent")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceReferentDTO> Get_EvidenceIndicators_ByReferent(int id_user)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics_ByReferent(id_user, usr); 
        }

        [HttpGet("Get_EvidenceIndicators_ByTenant")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceIndicators_ByTenant(int id_tenant)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics_ByTenant(id_tenant, usr); 
        }

        [HttpGet("Get_EvidenceIndicators_ByUnitMeasure")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceIndicators_ByUnitMeasure(int id_unit_measure)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics_ByUnitMeasure(id_unit_measure, usr); 
        }

        [HttpGet("Get_EvidenceIndicators_ByCustomer")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceIndicators_ByCustomer(int id_customer)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics_ByCustomer(id_customer, usr); 
        }

        [HttpGet("Get_EvidenceIndicators_ByThreshold")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceIndicators_ByThreshold(int id_threshold)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics_ByThreshold(id_threshold, usr);
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceIndicators_ByService")]
        public List<EvidenceMetricDTO> Get_EvidenceIndicators_ByService(int id_service)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics_ByService(id_service, usr);
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceIndicators_ByTimeslot")]
        public List<EvidenceMetricDTO> Get_EvidenceIndicators_ByTimeslot(int id_timeslot)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceMetrics_ByTimeslot(id_timeslot, usr);
        }

        [HttpPost("Create_EvidenceIndicator")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceMetricDTO Create_EvidenceIndicator(EvidenceMetricDTO indicator)
        {
            var usr = (HttpContext.User) as AuthUser;
            var UserId = usr.UserId;
            var SessionToken = usr.SessionToken;
            return _evidenceAPI.Create_EvidenceMetric(indicator, UserId, SessionToken, usr); 
        }

        [HttpPost("Update_EvidenceIndicator")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceIndicator(EvidenceMetricDTO indicator)
        {
            return _evidenceAPI.Update_EvidenceMetric(indicator); 
        }
        [HttpPost("Clone_EvidenceMetric_Parameters")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceMetricDTO Clone_EvidenceMetric_Parameters(EvidenceMetricDTO metric)
        {
            var usr = (HttpContext.User) as AuthUser;
            var UserId = usr.UserId;
            var SessionToken = usr.SessionToken;
            return _evidenceAPI.Clone_EvidenceMetric_Parameters(metric, UserId, SessionToken, usr); 
        }

        [HttpPost("Delete_EvidenceIndicator_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceIndicator_ById(int id_indicator)
        {
            return _evidenceAPI.Delete_EvidenceMetric_ById(id_indicator); 
        }

        [HttpPost("Delete_EvidenceIndicator_ByVersion")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceIndicator_ByVersion(int id_version)
        {
            return _evidenceAPI.Delete_EvidenceMetric_ByVersion(id_version); 
        }

        [HttpGet("Get_EvidenceLogs")]
        public List<EvidenceLogDTO> Get_EvidenceLogs()
        {
            return _evidenceAPI.Get_EvidenceLogs(); 
        }

        [HttpGet("Get_EvidenceReferents")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceReferentDTO> Get_EvidenceReferents()
        {
            return _evidenceAPI.Get_EvidenceReferents(); 
        }

        [HttpGet("Get_EvidenceReferents_ByIndicator")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceReferentDTO> Get_EvidenceReferents_ByIndicator(int id_indicator)
        {
            return _evidenceAPI.Get_EvidenceReferents_ByMetric(id_indicator); 
        }

        [HttpPost("Create_EvidenceReferent")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Create_EvidenceReferent(EvidenceReferentDTO referent)
        {
            return _evidenceAPI.Create_EvidenceReferent(referent); 
        }

        [HttpPost("Update_EvidenceReferent")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceReferent(EvidenceReferentDTO referent)
        {
            return _evidenceAPI.Update_EvidenceReferent(referent); 
        }

        [HttpPost("Delete_EvidenceReferent_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceReferent_ById(int id_referent)
        {
            return _evidenceAPI.Delete_EvidenceReferent_ById(id_referent); 
        }

        [HttpGet("Get_EvidenceTenants")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceTenantDTO> Get_EvidenceTenants()
        {
            return _evidenceAPI.Get_EvidenceTenants(); 
        }

        [HttpPost("Create_EvidenceTenant")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceTenantDTO Create_EvidenceTenant(EvidenceTenantDTO tenant)
        {
            return _evidenceAPI.Create_EvidenceTenant(tenant); 
        }

        [HttpPost("Update_EvidenceTenant")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceTenant(EvidenceTenantDTO tenant)
        {
            return _evidenceAPI.Update_EvidenceTenant(tenant); 
        }

        [HttpPost("Delete_EvidenceTenant_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceTenant_ById(int id_tenant)
        {
            return _evidenceAPI.Delete_EvidenceTenant_ById(id_tenant); 
        }

        [HttpGet("Get_EvidenceThresholds")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceThresholdDTO> Get_EvidenceThresholds()
        {
            return _evidenceAPI.Get_EvidenceThresholds(); 
        }

        [HttpGet("Get_EvidenceThreshold_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceThresholdDTO> Get_EvidenceThreshold_ById(int id_threshold)
        {
            return _evidenceAPI.Get_EvidenceThreshold_ById(id_threshold); 
        }

        [HttpGet("Get_EvidenceThresholds_ByIndicator")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceThresholdDTO> Get_EvidenceThresholds_ByIndicator(int id_indicator)
        {
            return _evidenceAPI.Get_EvidenceThresholds_ByMetric(id_indicator); 
        }

        [HttpPost("Assign_Evidence_IndicatorsThresholds")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Assign_Evidence_IndicatorsThresholds([FromBody] EvidenceMetricsThresholdsJSON json)
        {
            return _evidenceAPI.Assign_Evidence_MetricsThresholds(json); 
        }

        [HttpPost("Create_EvidenceThreshold")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceThresholdDTO Create_EvidenceThreshold(EvidenceThresholdDTO threshold)
        {
            return _evidenceAPI.Create_EvidenceThreshold(threshold); 
        }

        [HttpPost("Update_EvidenceThreshold")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceThreshold(EvidenceThresholdDTO threshold)
        {
            return _evidenceAPI.Update_EvidenceThreshold(threshold); 
        }

        [HttpPost("Delete_EvidenceThreshold_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceThreshold_ById(int id_threshold)
        {
            return _evidenceAPI.Delete_EvidenceThreshold_ById(id_threshold); 
        }

        [HttpGet("Get_EvidenceTimeslots")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceTimeslotDTO> Get_EvidenceTimeslots()
        {
            return _evidenceAPI.Get_EvidenceTimeslots(); 
        }

        [HttpGet("Get_EvidenceTimeslot_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceTimeslotDTO> Get_EvidenceTimeslot_ById(int id_timeslot)
        {
            return _evidenceAPI.Get_EvidenceTimeslot_ById(id_timeslot); 
        }

        [HttpPost("Create_EvidenceTimeslot")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceTimeslotDTO Create_EvidenceTimeslot(EvidenceTimeslotDTO timeslot)
        {
            var usr = (HttpContext.User) as AuthUser;
            var UserId = usr.UserId;
            var SessionToken = usr.SessionToken;
            return _evidenceAPI.Create_EvidenceTimeslot(timeslot, UserId, SessionToken); 
        }

        [HttpPost("Update_EvidenceTimeslot")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceTimeslot(EvidenceTimeslotDTO timeslot)
        {
            return _evidenceAPI.Update_EvidenceTimeslot(timeslot); 
        }

        [HttpPost("Delete_EvidenceTimeslot_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceTimeslot_ById(int id_timeslot)
        {
            return _evidenceAPI.Delete_EvidenceTimeslot_ById(id_timeslot); 
        }

        [HttpGet("Get_EvidenceUnitMeasures")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceUnitMeasureDTO> Get_EvidenceUnitMeasures()
        {
            return _evidenceAPI.Get_EvidenceUnitMeasures(); 
        }

        [HttpGet("Get_EvidenceUnitMeasure_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceUnitMeasureDTO> Get_EvidenceUnitMeasure_ById(int id_unit_measure)
        {
            return _evidenceAPI.Get_EvidenceUnitMeasure_ById(id_unit_measure); 
        }

        [HttpPost("Create_EvidenceUnitMeasure")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceUnitMeasureDTO Create_EvidenceUnitMeasure(EvidenceUnitMeasureDTO unit_measure)
        {
            var usr = (HttpContext.User) as AuthUser;
            var UserId = usr.UserId;
            var SessionToken = usr.SessionToken;
            return _evidenceAPI.Create_EvidenceUnitMeasure(unit_measure, UserId, SessionToken); 
        }

        [HttpPost("Update_EvidenceUnitMeasure")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceUnitMeasure(EvidenceUnitMeasureDTO unit_measure)
        {
            return _evidenceAPI.Update_EvidenceUnitMeasure(unit_measure); 
        }

        [HttpPost("Delete_EvidenceUnitMeasure_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceUnitMeasure_ById(int id_unit_measure)
        {
            return _evidenceAPI.Delete_EvidenceUnitMeasure_ById(id_unit_measure); 
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceUsers")]
        public List<SystemUserDTO> Get_EvidenceUsers()
        {
            return _evidenceAPI.Get_EvidenceUsers(); 
        }

        [HttpGet("Get_EvidenceUser_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<SystemUserDTO> Get_EvidenceUser_ById(int id_user)
        {
            return _evidenceAPI.Get_EvidenceUser_ById(id_user); 
        }

        [HttpGet("Get_EvidenceUsers_ByTenant")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<SystemUserDTO> Get_EvidenceUsers_ByTenant(int id_tenant)
        {
            return _evidenceAPI.Get_EvidenceUsers_ByTenant(id_tenant); 
        }

        [HttpPost("Create_EvidenceUser")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public SystemUserDTO Create_EvidenceUser(SystemUserDTO user)
        {
            var usr = (HttpContext.User) as AuthUser;
            var UserId = usr.UserId;
            var SessionToken = usr.SessionToken;
            return _evidenceAPI.Create_EvidenceUser(user, UserId, SessionToken); 
        }

        [HttpPost("Update_EvidenceUser")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceUser(SystemUserDTO user)
        {
            return _evidenceAPI.Update_EvidenceUser(user); 
        }

        [HttpPost("Delete_EvidenceUser_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceUser_ById(int id_user)
        {
            return _evidenceAPI.Delete_EvidenceUser_ById(id_user); 
        }

        [HttpGet("Get_EvidenceWorkflows")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceWorkflowDTO> Get_EvidenceWorkflows()
        {
            return _evidenceAPI.Get_EvidenceWorkflows(); 
        }

        [HttpGet("Get_EvidenceWorkflow_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceWorkflowDTO> Get_EvidenceWorkflow_ById(int id_workflow)
        {
            return _evidenceAPI.Get_EvidenceWorkflow_ById(id_workflow); 
        }

        [HttpGet("Get_EvidenceWorkflows_ByIndicator")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceWorkflowDTO> Get_EvidenceWorkflows_ByIndicator(int id_indicator)
        {
            return _evidenceAPI.Get_EvidenceWorkflows_ByMetric(id_indicator); 
        }

        [HttpGet("Get_EvidenceWorkflows_ByContract")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceWorkflowDTO> Get_EvidenceWorkflows_ByContract(int id_contract)
        {
            return _evidenceAPI.Get_EvidenceWorkflows_ByContract(id_contract); 
        }

        [HttpPost("Create_EvidenceWorkflow")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceWorkflowDTO Create_EvidenceWorkflow(EvidenceWorkflowDTO workflow)
        {
            var usr = (HttpContext.User) as AuthUser;
            var UserId = usr.UserId;
            var SessionToken = usr.SessionToken;
            return _evidenceAPI.Create_EvidenceWorkflow(workflow, UserId, SessionToken); 
        }

        [HttpPost("Update_EvidenceWorkflow")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceWorkflow(EvidenceWorkflowDTO workflow)
        {
            return _evidenceAPI.Update_EvidenceWorkflow(workflow); 
        }

        [HttpPost("Delete_EvidenceWorkflow_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceWorkflow_ById(int id_workflow)
        {
            return _evidenceAPI.Delete_EvidenceWorkflow_ById(id_workflow); 
        }

        [HttpGet("Get_EvidenceWorkflowsActions_ByWorkflow")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceWorkflowActionDTO> Get_EvidenceWorkflowsActions_ByWorkflow(int id_workflow)
        {
            return _evidenceAPI.Get_EvidenceWorkflowsActions_ByWorkflow(id_workflow); 
        }

        [HttpGet("Get_EvidenceWorkflowsAttachments_ByWorkflow")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceWorkflowAttachmentDTO> Get_EvidenceWorkflowsAttachments_ByWorkflow(int id_workflow)
        {
            return _evidenceAPI.Get_EvidenceWorkflowsAttachments_ByWorkflow(id_workflow); 
        }

        [HttpGet("Get_EvidenceWorkflowAttachment_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceWorkflowAttachmentDTO> Get_EvidenceWorkflowAttachment_ById(int id_attachment)
        {
            return _evidenceAPI.Get_EvidenceWorkflowAttachment_ById(id_attachment); 
        }

        [HttpPost("Create_EvidenceWorkflowAttachment")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Create_EvidenceWorkflowAttachment(EvidenceWorkflowAttachmentDTO attachment)
        {
            return _evidenceAPI.Create_EvidenceWorkflowAttachment(attachment); 
        }

        [HttpPost("Update_EvidenceWorkflowAttachment")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceWorkflowAttachment(EvidenceWorkflowAttachmentDTO attachment)
        {
            return _evidenceAPI.Update_EvidenceWorkflowAttachment(attachment); 
        }

        [HttpPost("Delete_EvidenceWorkflowAttachment_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceWorkflowAttachment_ById(int id_attachment)
        {
            return _evidenceAPI.Delete_EvidenceWorkflowAttachment_ById(id_attachment); 
        }

        [HttpGet("Get_EvidenceWorkflowsNotes_ByWorkflow")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceWorkflowNoteDTO> Get_EvidenceWorkflowsNotes_ByWorkflow(int id_workflow)
        {
            return _evidenceAPI.Get_EvidenceWorkflowsNotes_ByWorkflow(id_workflow); 
        }

        [HttpGet("Get_EvidenceWorkflowNote_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceWorkflowNoteDTO> Get_EvidenceWorkflowNote_ById(int id_note)
        {
            return _evidenceAPI.Get_EvidenceWorkflowNote_ById(id_note); 
        }

        [HttpPost("Create_EvidenceWorkflowNote")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Create_EvidenceWorkflowNote(EvidenceWorkflowNoteDTO note)
        {
            return _evidenceAPI.Create_EvidenceWorkflowNote(note); 
        }

        [HttpPost("Update_EvidenceWorkflowNote")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_EvidenceWorkflowNote(EvidenceWorkflowNoteDTO note)
        {
            return _evidenceAPI.Update_EvidenceWorkflowNote(note); 
        }

        [HttpPost("Delete_EvidenceWorkflowNote_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_EvidenceWorkflowNote_ById(int id_note)
        {
            return _evidenceAPI.Delete_EvidenceWorkflowNote_ById(id_note); 
        }

         [HttpGet("Get_KeyTimezone")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public  List<string> Get_KeyTimezone(string key)
        {
              List<string> result = _evidenceAPI.Get_KeyTimezone(key);
    
            if (result != null)
            {
                return result;
            }
    
            return new List<string>(); 
        }

        [Authorize(WorkFlowPermissions.VIEW_CONFIGURATION_GENERAL)]
        [HttpGet("GetAllBasicConfigurations")]
        public List<System_ConfigurationDTO> GetAllBasicConfigurations()
        {
            return _evidenceAPI.Get_EvidenceConfigurations_Basic();
        }

        [Authorize(WorkFlowPermissions.VIEW_CONFIGURATION_ADVANCED)]
        [HttpGet("GetAllAdvancedConfigurations")]
        public List<System_ConfigurationDTO> GetAllAdvancedConfigurations()
        {
            return _evidenceAPI.Get_EvidenceConfigurations_Advanced();
        }
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("GetConfiguration")]
        public System_ConfigurationDTO GetConfiguration(string owner, string key)
        {
            return _evidenceAPI.Get_EvidenceConfiguration_ByKey(owner, key);
        }

        [Authorize(WorkFlowPermissions.VIEW_CONFIGURATION_GENERAL)]
        [HttpGet("DeleteBasicConfiguration")]
        public void DeleteBasicConfiguration(string owner, string key)
        {
            _evidenceAPI.Delete_EvidenceConfiguration_ByKey(owner, key);
        }

        [Authorize(WorkFlowPermissions.VIEW_CONFIGURATION_ADVANCED)]
        [HttpGet("DeleteAdvancedConfiguration")]
        public void DeleteAdvancedConfiguration(string owner, string key)
        {
            _evidenceAPI.Delete_EvidenceConfiguration_ByKey(owner, key);
        }

        [Authorize(WorkFlowPermissions.VIEW_CONFIGURATION_GENERAL)]
        [HttpPost("AddUpdateBasicConfiguration")]
        public void AddUpdateBasicConfiguration([FromBody] System_ConfigurationDTO dto)
        {
            _evidenceAPI.AddUpdateBasicConfiguration(dto);
        }

        [Authorize(WorkFlowPermissions.VIEW_CONFIGURATION_ADVANCED)]
        [HttpPost("AddUpdateAdvancedConfiguration")]
        public void AddUpdateAdvancedConfiguration([FromBody] System_ConfigurationDTO dto)
        {
            _evidenceAPI.AddUpdateAdvancedConfiguration(dto);
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceWorkflowSteps")]
        public List<EvidenceWorkFlowStepsDTO> Get_EvidenceWorkflowSteps()
        {
            return _evidenceAPI.Get_EvidenceWorkflowSteps();
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceTimezones")]
        public List<EvidenceTimeZonesDTO> Get_EvidenceTimezones()
        {
            return _evidenceAPI.Get_EvidenceTimezones();
        }

        [HttpPost("Assign_Evidence_IndicatorsWorkflow")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Assign_Evidence_IndicatorsWorkflow([FromBody] EvidenceMetricsWorkflowJSON json)
        {
            return _evidenceAPI.Assign_Evidence_MetricsWorkflow(json);
        }

        [HttpPost("Assign_Evidence_WFTemplate_ToIndicator")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Assign_Evidence_WFTemplate_ToIndicator([FromBody] Evidence_MetricsWorkflowsDTO IndicatorsWorkflows)
        {
            return _evidenceAPI.Assign_Evidence_WFTemplate_ToMetric(IndicatorsWorkflows);
        }
        [HttpPost("MultiAssign_Evidence_WFTemplate_ToIndicator")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool MultiAssign_Evidence_WFTemplate_ToIndicator([FromBody] Evidence_MetricsWorkflowsDTO_multi IndicatorsWorkflows)
        {
            return _evidenceAPI.MultiAssign_Evidence_WFTemplate_ToMetric(IndicatorsWorkflows);
        }
        [HttpPost("Unassign_Evidence_WFTemplate_ToIndicator")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Unassign_Evidence_WFTemplate_ToIndicator([FromBody] Evidence_MetricsWorkflowsDTO IndicatorsWorkflows)
        {
            return _evidenceAPI.Unassign_Evidence_WFTemplate_ToMetric(IndicatorsWorkflows);
        }
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_Evidence_IndicatorsWorkflows")]
        public List<EvidenceMetricsWorkflowsDTO> Get_Evidence_IndicatorsWorkflows()
        {
            return _evidenceAPI.Get_Evidence_MetricsWorkflows();
        }
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_Workflow_Templates")]
        public List<Workflow_TemplatesDTO> Get_Workflow_Templates()
        {
            return _evidenceAPI.Get_Workflow_Templates();
        }
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_Evidence_IndicatorsWorkflows_ByTemplate")]
        public List<EvidenceMetricsWorkflowsDTO> Get_Evidence_IndicatorsWorkflows_ByTemplate(int id_wftemplate)
        {
            return _evidenceAPI.Get_Evidence_MetricsWorkflows_ByTemplate(id_wftemplate);
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_Evidence_IndicatorsWorkflows_ByIndicator")]
        public List<EvidenceMetricsWorkflowsDTO> Get_Evidence_IndicatorsWorkflows_ByIndicator(int id_indicator)
        {
            return _evidenceAPI.Get_Evidence_MetricsWorkflows_ByMetric(id_indicator);
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_Evidence_IndicatorsWorkflows_ByContract")]
        public List<EvidenceMetricsWorkflowsDTO> Get_Evidence_IndicatorsWorkflows_ByContract(int id_contract)
        {
            return _evidenceAPI.Get_Evidence_MetricsWorkflows_ByContract(id_contract);
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_Evidence_IndicatorsWorkflows_ByContractByTemplate")]
        public List<EvidenceMetricsWorkflowsDTO> Get_Evidence_IndicatorsWorkflows_ByContractByTemplate(int id_contract, int id_wftemplate)
        {
            return _evidenceAPI.Get_Evidence_MetricsWorkflows_ByContractByTemplate(id_contract, id_wftemplate);
        }

        [HttpGet("Get_EvidenceIndicators_ByContract_ByTemplate")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceMetricDTO> Get_EvidenceIndicators_ByContract_ByTemplate(int id_contract, int id_wftemplate)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceIndicators_ByContract_ByTemplate(id_contract, id_wftemplate, usr);
        }
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceFreeFormReports")]
        public List<EvidenceFreeFormReportDTO> Get_EvidenceFreeFormReports()
        {
            return _evidenceAPI.Get_EvidenceFreeFormReports();
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceFreeFormReport_ById")]
        public EvidenceFreeFormReportDTO Get_EvidenceFreeFormReport_ById(int id_freeformreport)
        {
            return _evidenceAPI.Get_EvidenceFreeFormReport_ById(id_freeformreport);
        }
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceServices")]
        public List<EvidenceServicesDTO> Get_EvidenceServices()
        {
            return _evidenceAPI.Get_EvidenceServices();
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpGet("Get_EvidenceService_ById")]
        public List<EvidenceServicesDTO> Get_EvidenceService_ById(int id_service)
        {
            return _evidenceAPI.Get_EvidenceService_ById(id_service);
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpPost("Create_EvidenceService")]
        public EvidenceServicesDTO Create_EvidenceService(EvidenceServicesDTO service)
        {
            return _evidenceAPI.Create_EvidenceService(service);
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpPost("Update_EvidenceService")]
        public bool Update_EvidenceService(EvidenceServicesDTO service)
        {
            return _evidenceAPI.Update_EvidenceService(service);
        }

        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        [HttpPost("Delete_EvidenceService_byId")]
        public bool Delete_EvidenceService_byId(int id_service)
        {
            return _evidenceAPI.Delete_EvidenceService_byId(id_service);
        }

        [HttpGet("Get_EvidenceCustomers_ByContract")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceCustomerDTO> Get_EvidenceCustomers_ByContract(int id_contract)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceCustomers_ByContract(id_contract, usr);
        }

        [HttpGet("Get_EvidenceWFTemplatesContracts")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<Workflow_TemplateContractDTO> Get_EvidenceWFTemplatesContracts()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_EvidenceWFTemplatesContracts();
        }
        [HttpPost("Assign_Evidence_ContractCustomers")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Assign_Evidence_ContractCustomers([FromBody] EvidenceContractCustomersJSON json)
        {
            return _evidenceAPI.Assign_Evidence_ContractCustomers(json);
        }
        [HttpPost("Assign_Evidence_ContractCustomer")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Assign_Evidence_ContractCustomer(int id_contract, int id_customer, string customer_type)
        {
            return _evidenceAPI.Assign_Evidence_ContractCustomer(id_contract, id_customer, customer_type);
        }
        [HttpPost("Update_Evidence_ContractCustomer")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_Evidence_ContractCustomer(int idContractCustomer, string customer_type)
        {
            return _evidenceAPI.Update_Evidence_ContractCustomer(idContractCustomer, customer_type);
        }
        [HttpPost("Delete_Evidence_ContractCustomer")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_Evidence_ContractCustomer(int idContractCustomer)
        {
            return _evidenceAPI.Delete_Evidence_ContractCustomer(idContractCustomer);
        }
        [HttpPost("Assign_Evidence_IndicatorThreshold")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public IActionResult Assign_Evidence_IndicatorThreshold(int id_contract, int id_indicator, int id_threshold)
        {
            try
            {
                return Ok(_evidenceAPI.Assign_Evidence_IndicatorThreshold(id_contract, id_indicator, id_threshold));
            }
            catch(Exception e)
            {
                var json3 = new { error = "Errore durante l'inserimento", description = e.Message.ToString() };
                return StatusCode(500, json3);
            }
        }
        [HttpPost("Delete_Evidence_IndicatorThreshold")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_Evidence_IndicatorThreshold(int id_threshold)
        {
            return _evidenceAPI.Delete_Evidence_IndicatorThreshold(id_threshold);
        }
        [HttpGet("Get_CLM_Fields")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<CLM_FieldDTO> Get_CLM_Fields()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_CLM_Fields();
        }
        [HttpGet("Get_CLM_Organizations")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<CLM_OrganizationDTO> Get_CLM_Organizations()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_CLM_Organizations();
        }
        [HttpGet("Get_CLM_UsersOrganization_ByOrganization")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<CLM_UserOrganizationDTO> Get_CLM_UsersOrganization_ByOrganization(int id_organization)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_CLM_UsersOrganization_ByOrganization(id_organization);
        }
        [HttpGet("Get_CLM_UsersOrganization_ByUser")]
        public List<CLM_UserOrganizationDTO> Get_CLM_UsersOrganization_ByUser(int id_user)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_CLM_UsersOrganization_ByUser(id_user);
        }
        [HttpPost("Assign_CLM_UsersOrganization")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Assign_CLM_UsersOrganization([FromBody] CLM_Assign_UserOrganizationDTO json)
        {
            return _evidenceAPI.Assign_CLM_UsersOrganization(json);
        }
        [HttpGet("Get_CLM_Workflows")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<CLM_WorkflowDTO> Get_CLM_Workflows()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_CLM_Workflows();
        }
        [HttpGet("Get_CLM_Workflow_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public CLM_WorkflowDTO Get_CLM_Workflow_ById(int id)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_CLM_Workflow_ById(id);
        }
        [HttpGet("Get_CLM_Templates")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<CLM_TemplateDTO> Get_CLM_Templates()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_CLM_Templates();
        }
        [HttpGet("Get_CLM_Template_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public CLM_TemplateDTO Get_CLM_Template_ById(int id)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_CLM_Template_ById(id);
        }
        [HttpPost("Create_CLM_Organization")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Create_CLM_Organization([FromBody] CLM_OrganizationDTO organization)
        {
            return _evidenceAPI.Create_CLM_Organization(organization);
        }
        [HttpPost("Create_CLM_Field")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Create_CLM_Field([FromBody] CLM_FieldDTO field)
        {
            return _evidenceAPI.Create_CLM_Field(field);
        }
        [HttpPost("Create_CLM_Workflow")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Create_CLM_Workflow([FromBody] CLM_WorkflowDTO workflow)
        {
            return _evidenceAPI.Create_CLM_Workflow(workflow);
        }
        [HttpPost("Update_CLM_Organization")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_CLM_Organization([FromBody] CLM_OrganizationDTO organization)
        {
            return _evidenceAPI.Update_CLM_Organization(organization);
        }
        [HttpPost("Create_CLM_Template")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Create_CLM_Template([FromBody] CLM_TemplateDTO Template)
        {
            return _evidenceAPI.Create_CLM_Template(Template);
        }
        [HttpPost("Update_CLM_Field")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_CLM_Field([FromBody] CLM_FieldDTO field)
        {
            return _evidenceAPI.Update_CLM_Field(field);
        }
        [HttpPost("Update_CLM_Workflow")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_CLM_Workflow([FromBody] CLM_WorkflowDTO workflow)
        {
            return _evidenceAPI.Update_CLM_Workflow(workflow);
        }
        [HttpPost("Update_CLM_Template")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_CLM_Template([FromBody] CLM_TemplateDTO Template)
        {
            return _evidenceAPI.Update_CLM_Template(Template);
        }
        [HttpPost("Delete_CLM_Field")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_CLM_Field(int id_field)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Delete_CLM_Field(id_field, usr);
        }
        [HttpPost("Delete_CLM_Organization")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_CLM_Organization(int id_organization)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Delete_CLM_Organization(id_organization, usr);
        }
        [HttpPost("Delete_CLM_Workflow")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_CLM_Workflow(int id_workflow)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Delete_CLM_Workflow(id_workflow, usr);
        }
        [HttpPost("Delete_CLM_Template")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_CLM_Template(int id_template)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Delete_CLM_Template(id_template, usr);
        }
       /* public class ffr
        {
            public int id { get; set; }
            public string name { get; set; }
            public List<ffrdata> data { get; set; }
        }*/
        public class ffrdata
        {
            public float count { get; set; }
            public int event_type_id { get; set; }
            public int resource_id { get; set; }
        }
        [HttpGet("Get_FFR_List")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public List<EvidenceFreeFormReportDTO> Get_FFR_List()
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Get_FFR_List(usr);
        }
        [HttpPost("Update_FFR")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Update_FFR([FromBody] EvidenceFreeFormReportDTO ffr)
        {
            return _evidenceAPI.Update_FFR_ById(ffr);
        }
        [HttpPost("Delete_FFR_ById")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Delete_FFR_ById(int id)
        {
            return _evidenceAPI.Delete_FFR_ById(id);
        }
        [HttpPost("Create_FFR")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public bool Create_FFR([FromBody] EvidenceFreeFormReportDTO ffr)
        {
            var usr = (HttpContext.User) as AuthUser;
            return _evidenceAPI.Create_FFR(ffr, usr.UserId);
        }
        [HttpGet("Get_Widget_Data_byId")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public IActionResult Get_Widget_Data_byId(int id, string type)
        {
            try
            {
                var result = _evidenceAPI.Get_Widget_Data_byId(id, type);
                return Ok(result);
            }
            catch(Exception e)
            {
                var jsonError = new { full = e.Message, error = "Casting error: Unable to convert the value of the 'result' column to an integer. Ensure all values are numeric"};
                return StatusCode(400, jsonError);
            }
        }
        [HttpPost("Get_FFR_Data_byId")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceFreeFormReportDTO Get_FFR_Data_byId(int id, [FromBody] JsonDocument parameters)
        {
            return _evidenceAPI.Get_FFR_Data_byId(id, parameters);
        }
       /* [HttpPost("Get_FFR_Data_byId")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public EvidenceFreeFormReportDTO Get_FFR_Data_byId_body(int id, [FromBody] JsonDocument parameters)
        {
            return _evidenceAPI.Get_FFR_Data_byId_body(id, parameters);
        }*/
        [HttpPost("Get_FFR_Chart_byId")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public IActionResult Get_FFR_Chart_byId(int id, [FromBody] JsonDocument parameters)
        {
            return _evidenceAPI.Get_FFR_Chart_byId(id, parameters);
        }
        
       /* [HttpPost("Get_FFR_Chart_byId")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public IActionResult Get_FFR_Chart_byId(int id)
        {
            return _evidenceAPI.Get_FFR_Chart_byId(id);
        }*/
        [HttpGet("Get_xls_chart")]
        [Authorize(WorkFlowPermissions.BASIC_LOGIN)]
        public IActionResult Get_xls_chart()
        {
            return BackgroundExport2();
        }
        public class calcdata
        {
            public int id_indicator { get; set; }
            public float value { get; set; }
            public int month { get; set; }
            public int year { get; set; }
        }
        public class testChart
        {
            public byte[] byteStream { get; set; }
            public byte[] blob { get; set; }
        }
        public IActionResult BackgroundExport2()
        {
            try
            {
                    int month = 3;
                    int year = 2022;
                    var day = DateTime.DaysInMonth(Convert.ToInt32(year), Convert.ToInt32(month));
                    string folderpath = "/data/log/rawdata";
                    string extension = ".xlsx";
                    int length = Directory.GetDirectories(folderpath).Length;
                    string foldername = "rawdataexport-" + (length + 1).ToString() + "-" + month + year;
                    string completefolderpath = System.IO.Path.Combine(folderpath, foldername);
                    string contractFake = "Contract_name";
                    string indicatorFake = "Indicator_name";
                    string contractname = contractFake.Trim().Replace(" ", "_").Replace("/", "_").Replace("\\", "_");
                    string IdKPI = indicatorFake.Trim().Replace(" ", "").Replace("/", "_").Replace("\\", "_");
                    if (!Directory.Exists(completefolderpath))
                    {
                        Directory.CreateDirectory(completefolderpath);
                    }
                    string filename = contractname + "-" + IdKPI + "-" + month + "-" + year + extension;
                    string completefilepath = System.IO.Path.Combine(completefolderpath, filename);
                var file_data = new List<calcdata>();
                var rand = new Random();
                for (int i = 1; i <= 12; i++)
                {
                    var raw_data = new calcdata()
                    {
                        id_indicator = 1,
                        year = 2022,
                        month = i,
                        value = rand.Next(100)
                    };
                    file_data.Add(raw_data);
                }
                DataTable ViewTable = new DataTable();
                ViewTable.Columns.Add("id_indicator");
                ViewTable.Columns.Add("year");
                ViewTable.Columns.Add("month");
                ViewTable.Columns.Add("value");
                DataTable data = ToDataTable(file_data);
                SLDocument sl = new SLDocument();
                sl.AddWorksheet(contractname);
                sl.DeleteWorksheet("Sheet1");
                var currentRow = 1;
                var currentColumn = 1;
                foreach (DataColumn col in ViewTable.Columns)
                {
                    sl.SetCellValue(currentRow, currentColumn, col.ColumnName);
                    currentColumn++;
                }
                foreach (DataRow row in data.Rows)
                {
                    currentColumn = 1;
                    currentRow++;
                    sl.SetCellValue(currentRow, currentColumn, int.Parse(row["id_indicator"].ToString()));
                    currentColumn++;
                    sl.SetCellValue(currentRow, currentColumn, int.Parse(row["year"].ToString()));
                    currentColumn++;
                    sl.SetCellValue(currentRow, currentColumn, int.Parse(row["month"].ToString()));
                    currentColumn++;
                    sl.SetCellValue(currentRow, currentColumn, float.Parse(row["value"].ToString()));
                    currentColumn++;
                }
                SLChart chart = sl.CreateChart(contractname, 1,3,13,4);
                    chart.SetChartType(SLColumnChartType.ClusteredColumn);
                    chart.SetChartPosition(7, 1, 22, 8.5);
                    chart.ShowChartTitle(true);
                    chart.ChartName = "Evidence";
                    sl.InsertChart(chart);
                sl.SaveAs(completefilepath);

                byte[] fileDataByte = null;
                using (FileStream fs = new FileStream(completefilepath, FileMode.Open))
                {
                    using (BinaryReader binaryReader = new BinaryReader(fs))
                    {
                        fileDataByte = binaryReader.ReadBytes((int)fs.Length);
                        return new FileContentResult(fileDataByte, "application/octet-stream")
                        {
                            FileDownloadName = filename
                        };
                    }
                }
                
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private static DataTable ToDataTable<T>(List<T> items){
            DataTable dataTable = new DataTable(typeof(T).Name);
            PropertyInfo[] Props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (PropertyInfo prop in Props)
            {
                var type = (prop.PropertyType.IsGenericType && prop.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>) ? Nullable.GetUnderlyingType(prop.PropertyType) : prop.PropertyType);
                dataTable.Columns.Add(prop.Name, type);
            }
            foreach (T item in items)
            {
                var values = new object[Props.Length];
                for (int i = 0; i < Props.Length; i++)
                {
                    values[i] = Props[i].GetValue(item, null);
                }
                dataTable.Rows.Add(values);
            }
            return dataTable;
        }


    }
}

// licenza a pagamento
/*Spire.Xls.Workbook book = new Spire.Xls.Workbook();
book.LoadFromFile(completefilepath);
Spire.Xls.Worksheet sheet = book.Worksheets[contractname];
//Add chart and set chart data range  
Spire.Xls.Chart chart = sheet.Charts.Add(ExcelChartType.ColumnClustered);
chart.DataRange = sheet.Range["D1:D13"];
chart.SeriesDataFromRange = false;
//Chart border  
chart.ChartArea.Border.Weight = ChartLineWeightType.Medium;
chart.ChartArea.Border.Color = Color.SandyBrown;
//Chart position  
chart.LeftColumn = 2;
chart.TopRow = 11;
chart.RightColumn = 12;
chart.BottomRow = 21;
//Chart title  
chart.ChartTitle = "Report - Year";
chart.ChartTitleArea.FontName = "Calibri";
chart.ChartTitleArea.Size = 13;
chart.ChartTitleArea.IsBold = true;
//Chart axis  
chart.PrimaryCategoryAxis.Title = "Month";
chart.PrimaryCategoryAxis.Font.Color = Color.Blue;
chart.PrimaryValueAxis.Title = "Value";
chart.PrimaryValueAxis.HasMajorGridLines = false;
chart.PrimaryValueAxis.MaxValue = 100;
chart.PrimaryValueAxis.TitleArea.TextRotationAngle = 90;
//Chart legend  
chart.Legend.Position = LegendPositionType.Right;
book.SaveToFile(completefilepath+".chart.xlsx", ExcelVersion.Version2010);*/